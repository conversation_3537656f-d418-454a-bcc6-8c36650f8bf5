#!/usr/bin/env python3
"""
Test script to verify agent connection configuration.

This script tests:
1. ADKHostManager initialization with agent addresses
2. Agent connection establishment
3. File upload message creation
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root / "samples" / "python"))

async def test_adk_host_manager():
    """Test ADKHostManager configuration."""
    print("Testing ADKHostManager configuration...")
    
    try:
        import httpx
        from service.server.adk_host_manager import ADKHostManager
        
        # Create HTTP client
        async with httpx.AsyncClient() as client:
            # Test with environment variables
            os.environ['FILE_TYPE_AGENT_HOST'] = 'localhost'
            os.environ['FILE_TYPE_AGENT_PORT'] = '8001'
            
            # Create ADKHostManager
            manager = ADKHostManager(
                http_client=client,
                api_key='test_key',
                uses_vertex_ai=False
            )
            
            # Check if remote agent addresses are configured
            addresses = manager._get_remote_agent_addresses()
            print(f"✓ Configured agent addresses: {addresses}")
            
            # Verify file type agent is included
            expected_url = 'http://localhost:8001/'
            if expected_url in addresses:
                print(f"✓ File type detection agent configured: {expected_url}")
            else:
                print(f"❌ File type detection agent not found in addresses")
            
            return True
            
    except Exception as e:
        print(f"❌ ADKHostManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_file_part_creation():
    """Test FilePart creation for file uploads."""
    print("\nTesting FilePart creation...")
    
    try:
        import base64
        from a2a.types import Message, Part, Role, TextPart, FilePart, FileWithBytes
        
        # Create test file content
        test_content = b"This is a test file for analysis."
        file_data = base64.b64encode(test_content).decode('utf-8')
        
        # Create message with both text and file parts
        message = Message(
            role=Role.user,
            parts=[
                Part(root=TextPart(text="Please analyze this file")),
                Part(root=FilePart(
                    file=FileWithBytes(
                        name="test.txt",
                        bytes=file_data,
                        mime_type="text/plain"
                    )
                ))
            ],
            message_id="test_msg_123",
            context_id="test_ctx_456",
        )
        
        print(f"✓ Created message with {len(message.parts)} parts")
        print(f"✓ Text part: {message.parts[0].root.text[:30]}...")
        print(f"✓ File part: {message.parts[1].root.file.name}")
        print(f"✓ File size: {len(message.parts[1].root.file.bytes)} bytes (base64)")
        
        return True
        
    except Exception as e:
        print(f"❌ FilePart creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_health_check():
    """Test if file type detection agent is running."""
    print("\nTesting agent health check...")
    
    try:
        import httpx
        
        agent_url = "http://localhost:8001/"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{agent_url}health", timeout=5.0)
                if response.status_code == 200:
                    print(f"✅ File type detection agent is running at {agent_url}")
                    return True
                else:
                    print(f"⚠️  Agent responded with status {response.status_code}")
                    return False
            except httpx.ConnectError:
                print(f"❌ Cannot connect to agent at {agent_url}")
                print("   Make sure the file type detection agent is running:")
                print("   cd ../../samples/python/agents/file-type-detector")
                print("   python __main__.py --host localhost --port 8001")
                return False
            except Exception as e:
                print(f"❌ Health check failed: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Health check test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Testing Agent Connection Configuration")
    print("=" * 50)
    
    tests = [
        test_adk_host_manager,
        test_file_part_creation,
        test_agent_health_check,
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ Configuration is correct. You can now:")
        print("   1. Start the file type detection agent")
        print("   2. Start the demo UI")
        print("   3. Upload files through the chat interface")
        return 0
    else:
        print(f"❌ Some tests failed ({passed}/{total})")
        print("\n🔧 Please fix the issues above before proceeding.")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

# Demo UI 路由修复说明

## 问题描述

Demo UI 出现连接错误的根本原因是 **路由配置冲突**：

```
Failed to list conversations: All connection attempts failed
Failed to list tasks All connection attempts failed
Error getting pending messages All connection attempts failed
```

## 根本原因

在 `main.py` 中，Mesop UI 被挂载到根路径 `/`，这覆盖了所有 API 路由：

```python
# 问题代码
app.mount('/', WSGIMiddleware(me.create_wsgi_app(...)))
```

这导致：
- API 端点（如 `/conversation/list`）无法访问
- 前端无法获取对话、任务等数据
- 出现 "All connection attempts failed" 错误

## 修复方案

### 1. 修改路由配置

将 Mesop UI 挂载到 `/ui` 路径，保留 API 路由：

```python
# 修复后的代码
app.mount('/ui', WSGIMiddleware(me.create_wsgi_app(...)))

@app.get("/")
async def root():
    return RedirectResponse(url="/ui")
```

### 2. 修复客户端连接地址

```python
# 修复前
server_url = 'http://0.0.0.0:12000'  # 无效的客户端地址

# 修复后  
server_url = 'http://127.0.0.1:12000'  # 正确的客户端地址
```

## 修复后的访问方式

### API 端点
- `/conversation/list` - 列出对话
- `/task/list` - 列出任务
- `/agent/list` - 列出代理
- `/message/pending` - 获取待处理消息

### UI 访问
- `http://127.0.0.1:12000/` - 自动重定向到 UI
- `http://127.0.0.1:12000/ui` - 直接访问 UI

## 验证修复

### 1. 启动 Demo UI
```bash
cd demo/ui
source ../../.venv/Scripts/activate
python main.py
```

### 2. 测试 API 端点
```bash
# 测试对话列表
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"method": "conversation/list"}' \
  http://127.0.0.1:12000/conversation/list

# 测试任务列表
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"method": "task/list"}' \
  http://127.0.0.1:12000/task/list
```

### 3. 访问 UI
打开浏览器访问 `http://127.0.0.1:12000`，应该：
- 自动重定向到 `/ui`
- 不再显示连接错误
- 能够正常使用文件上传功能

## 预期结果

修复后，Demo UI 应该：

1. **正常启动**：无连接错误
2. **API 可访问**：所有端点正常响应
3. **UI 功能完整**：
   - 对话列表正常显示
   - 文件上传功能可用
   - 消息发送正常工作
   - 任务状态正确显示

## 故障排除

如果仍有问题：

1. **检查端口**：确保 12000 端口未被占用
2. **检查防火墙**：确保本地连接允许
3. **查看日志**：检查是否有其他错误信息
4. **重启服务**：完全重启 Demo UI

## 技术细节

### 路由优先级
FastAPI 按注册顺序处理路由：
1. 先注册 API 路由（`ConversationServer`）
2. 后挂载 Mesop UI（`WSGIMiddleware`）
3. 添加根路径重定向

### 中间件处理
- API 请求直接由 FastAPI 处理
- UI 请求由 Mesop WSGI 应用处理
- 根路径请求重定向到 UI

这样确保了 API 和 UI 的完全分离和正常工作。

"""MCP Client for testing Security Analysis tools."""

import asyncio
import json
import logging
from typing import Dict, Any, Optional

import aiohttp
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class SecurityAnalysisMCPClient:
    """Client for testing Security Analysis MCP server."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the MCP client.
        
        Args:
            base_url: Base URL of the MCP server
        """
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to call
            parameters: Parameters to pass to the tool
            
        Returns:
            Tool execution result
            
        Raises:
            RuntimeError: If the request fails
        """
        if not self.session:
            raise RuntimeError("Client session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/tools/{tool_name}"
        
        try:
            async with self.session.post(url, json=parameters) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            raise RuntimeError(f"Failed to call tool {tool_name}: {str(e)}")
    
    async def test_code_insight_analysis(
        self, 
        api_key: str, 
        file_type: str = "Python", 
        code_sample: str = "print('Hello, World!')"
    ) -> Dict[str, Any]:
        """Test the CodeInsight analysis tool.
        
        Args:
            api_key: API key for CodeInsight service
            file_type: Type of code file
            code_sample: Code to analyze
            
        Returns:
            Analysis result
        """
        logger.info("Testing CodeInsight analysis tool")
        
        parameters = {
            "api_key": api_key,
            "file_type": file_type,
            "prompt": code_sample
        }
        
        return await self.call_tool("code_insight_analysis", parameters)
    
    async def test_qde_analysis(self, input_path: str) -> Dict[str, Any]:
        """Test the QDE malicious code analysis tool.
        
        Args:
            input_path: Path to file or directory to analyze
            
        Returns:
            Analysis result
        """
        logger.info("Testing QDE malicious code analysis tool")
        
        parameters = {
            "input_path": input_path
        }
        
        return await self.call_tool("qde_malicious_code_analysis", parameters)
    
    async def list_tools(self) -> Dict[str, Any]:
        """List available tools on the server.
        
        Returns:
            List of available tools
        """
        if not self.session:
            raise RuntimeError("Client session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/tools"
        
        try:
            async with self.session.get(url) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            raise RuntimeError(f"Failed to list tools: {str(e)}")


async def main():
    """Main function to demonstrate client usage."""
    logging.basicConfig(level=logging.INFO)
    
    async with SecurityAnalysisMCPClient() as client:
        try:
            # List available tools
            print("=== Available Tools ===")
            tools = await client.list_tools()
            print(json.dumps(tools, indent=2))
            
            # Test CodeInsight analysis (requires API key)
            api_key = input("Enter CodeInsight API key (or press Enter to skip): ").strip()
            if api_key:
                print("\n=== Testing CodeInsight Analysis ===")
                code_sample = """
import os
import subprocess

def execute_command(cmd):
    return subprocess.run(cmd, shell=True, capture_output=True)

user_input = input("Enter command: ")
result = execute_command(user_input)
print(result.stdout)
"""
                try:
                    result = await client.test_code_insight_analysis(
                        api_key=api_key,
                        file_type="Python",
                        code_sample=code_sample
                    )
                    print("CodeInsight Result:")
                    print(json.dumps(result, indent=2))
                except Exception as e:
                    print(f"CodeInsight test failed: {e}")
            
            # Test QDE analysis (requires valid path)
            test_path = input("Enter path for QDE analysis (or press Enter to skip): ").strip()
            if test_path:
                print("\n=== Testing QDE Analysis ===")
                try:
                    result = await client.test_qde_analysis(test_path)
                    print("QDE Result:")
                    print(json.dumps(result, indent=2))
                except Exception as e:
                    print(f"QDE test failed: {e}")
            
        except Exception as e:
            print(f"Client error: {e}")


if __name__ == "__main__":
    asyncio.run(main())

"""Tests for security analysis tools."""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock
from security_analysis_mcp.tools import code_insight_analysis, qde_malicious_code_analysis


class TestCodeInsightAnalysis:
    """Tests for CodeInsight analysis tool."""
    
    @pytest.mark.asyncio
    async def test_code_insight_analysis_success(self):
        """Test successful CodeInsight analysis."""
        with patch('aiohttp.ClientSession') as mock_session:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.text.return_value = "Analysis complete: No security issues found"
            mock_response.raise_for_status.return_value = None
            
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
            
            result = await code_insight_analysis(
                api_key="test_key",
                file_type="Python", 
                prompt="print('hello')"
            )
            
            assert result == "Analysis complete: No security issues found"
    
    @pytest.mark.asyncio
    async def test_code_insight_analysis_missing_params(self):
        """Test CodeInsight analysis with missing parameters."""
        with pytest.raises(ValueError, match="All parameters .* are required"):
            await code_insight_analysis("", "Python", "code")
        
        with pytest.raises(ValueError, match="All parameters .* are required"):
            await code_insight_analysis("key", "", "code")
        
        with pytest.raises(ValueError, match="All parameters .* are required"):
            await code_insight_analysis("key", "Python", "")


class TestQDEAnalysis:
    """Tests for QDE analysis tool."""
    
    @pytest.mark.asyncio
    async def test_qde_analysis_invalid_path(self):
        """Test QDE analysis with invalid path."""
        with pytest.raises(ValueError, match="Invalid path"):
            await qde_malicious_code_analysis("/nonexistent/path")
    
    @pytest.mark.asyncio
    async def test_qde_analysis_missing_param(self):
        """Test QDE analysis with missing parameter."""
        with pytest.raises(ValueError, match="input_path parameter is required"):
            await qde_malicious_code_analysis("")
    
    @pytest.mark.asyncio
    async def test_qde_analysis_success(self):
        """Test successful QDE analysis."""
        with patch('pathlib.Path.exists', return_value=True), \
             patch('asyncio.create_subprocess_exec') as mock_subprocess:
            
            # Mock successful subprocess execution
            mock_process = AsyncMock()
            mock_process.communicate.return_value = (b"Analysis complete", b"")
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process
            
            result = await qde_malicious_code_analysis("/tmp/test_file.py")
            
            assert result == "Analysis complete"

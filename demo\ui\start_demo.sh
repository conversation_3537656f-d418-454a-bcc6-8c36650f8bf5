#!/bin/bash

# Demo UI Startup Script
# This script helps start the demo UI with proper environment setup

echo "🚀 Starting A2A Demo UI..."
echo

# Check if virtual environment exists
if [ ! -d "../../.venv" ]; then
    echo "❌ Virtual environment not found at ../../.venv"
    echo "Please create and activate the virtual environment first:"
    echo "  cd ../../"
    echo "  python -m venv .venv"
    echo "  source .venv/Scripts/activate  # On Windows"
    echo "  # or source .venv/bin/activate  # On Linux/Mac"
    echo "  pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source ../../.venv/Scripts/activate

# Check if required packages are installed
echo "🔍 Checking dependencies..."
python -c "import mesop, a2a" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Required packages not installed. Installing dependencies..."
    pip install -e ../../
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Check if backend service is running
echo "🔗 Checking backend service connection..."
curl -s http://localhost:12000/health >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  Backend service not detected at localhost:12000"
    echo "   The demo UI will run in standalone mode with limited functionality."
    echo "   To start the full multiagent system:"
    echo "   1. Open another terminal"
    echo "   2. cd samples/python/hosts/multiagent"
    echo "   3. source ../../../../.venv/Scripts/activate"
    echo "   4. python __main__.py"
    echo
fi

# Set environment variables
export GOOGLE_API_KEY=${GOOGLE_API_KEY:-""}
export A2A_UI_HOST=${A2A_UI_HOST:-"0.0.0.0"}
export A2A_UI_PORT=${A2A_UI_PORT:-"12000"}

echo "🌐 Starting demo UI on http://${A2A_UI_HOST}:${A2A_UI_PORT}"
echo "📁 Working directory: $(pwd)"
echo

# Start the demo UI
python main.py

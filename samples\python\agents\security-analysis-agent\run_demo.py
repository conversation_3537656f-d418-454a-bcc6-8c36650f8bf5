"""Demo script to showcase Security Analysis Agent capabilities."""

import logging
import os
import subprocess
import time
from typing import Optional

from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


def start_mcp_server() -> Optional[subprocess.Popen]:
    """Start the MCP server in background."""
    try:
        print("🚀 Starting Security Analysis MCP Server...")
        
        process = subprocess.Popen(
            ["python", "-m", "security_analysis_mcp.server", 
             "--host", "localhost", "--port", "8000"],
            cwd="../security-analysis-mcp"
        )
        
        # Wait for server to start
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ MCP Server started successfully on http://localhost:8000")
            return process
        else:
            print("❌ Failed to start MCP Server")
            return None
            
    except Exception as e:
        print(f"❌ Error starting MCP Server: {e}")
        return None


def run_demo():
    """Run the security analysis demo."""
    print("=" * 70)
    print("🔒 SECURITY ANALYSIS AGENT DEMO")
    print("=" * 70)
    
    # Check environment
    if not os.getenv('GOOGLE_API_KEY'):
        print("❌ GOOGLE_API_KEY not set. Please configure your environment.")
        return
    
    # Start MCP server
    server_process = start_mcp_server()
    if not server_process:
        return
    
    try:
        from agent import SecurityAnalysisAgent
        
        # Create agent
        print("\n🤖 Initializing Security Analysis Agent...")
        agent = SecurityAnalysisAgent()
        print("✅ Agent initialized successfully")
        
        # Demo 1: Analyze vulnerable Python code
        print("\n" + "="*50)
        print("📝 DEMO 1: Analyzing Vulnerable Python Code")
        print("="*50)
        
        vulnerable_code = """
import os
import subprocess
import pickle
import base64

def execute_user_command():
    # Vulnerability 1: Command injection
    user_input = input("Enter command: ")
    os.system(user_input)

def load_user_data():
    # Vulnerability 2: Unsafe deserialization
    user_data = input("Enter base64 data: ")
    data = base64.b64decode(user_data)
    return pickle.loads(data)

def sql_query(user_id):
    # Vulnerability 3: SQL injection
    query = f"SELECT * FROM users WHERE id = {user_id}"
    return query

# Execute functions
execute_user_command()
result = load_user_data()
query = sql_query(input("Enter user ID: "))
"""
        
        print("Analyzing code for security vulnerabilities...")
        result = agent.analyze_code_content(vulnerable_code, "Python")
        
        if result.error:
            print(f"❌ Analysis failed: {result.error}")
        else:
            print("✅ Analysis completed!")
            if result.security_findings:
                print(f"\n🔍 Security Findings:\n{result.security_findings}")
            if result.recommendations:
                print(f"\n💡 Recommendations:\n{result.recommendations}")
        
        # Demo 2: Analyze a test file (if QDE is available)
        print("\n" + "="*50)
        print("📁 DEMO 2: Analyzing Test File")
        print("="*50)
        
        test_file = "/tmp/demo_security_test.py"
        test_content = """
#!/usr/bin/env python3
import socket
import threading

def backdoor_listener(port):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.bind(('0.0.0.0', port))
    s.listen(1)
    
    while True:
        conn, addr = s.accept()
        data = conn.recv(1024)
        # Execute received commands
        import subprocess
        result = subprocess.run(data.decode(), shell=True, capture_output=True)
        conn.send(result.stdout)
        conn.close()

if __name__ == "__main__":
    threading.Thread(target=backdoor_listener, args=(4444,)).start()
"""
        
        # Create test file
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        print(f"Analyzing file: {test_file}")
        result = agent.analyze_file_path(test_file)
        
        # Clean up
        os.remove(test_file)
        
        if result.error:
            print(f"⚠️  File analysis result: {result.error}")
            print("(QDE engine may not be available in this environment)")
        else:
            print("✅ File analysis completed!")
            if result.malicious_patterns:
                print(f"\n⚠️  Malicious Patterns:\n{result.malicious_patterns}")
        
        # Demo 3: Interactive mode
        print("\n" + "="*50)
        print("🎮 DEMO 3: Interactive Mode")
        print("="*50)
        print("You can now interact with the agent directly.")
        print("Try commands like:")
        print("  health")
        print("  code Python 'import os; os.system(\"rm -rf /\")'")
        print("  quit")
        print()
        
        # Import and run interactive mode
        from __main__ import main as interactive_main
        interactive_main()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed.")
    except Exception as e:
        print(f"❌ Demo error: {e}")
    finally:
        # Stop MCP server
        if server_process:
            print("\n🛑 Stopping MCP Server...")
            server_process.terminate()
            server_process.wait()
            print("✅ MCP Server stopped")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    run_demo()

# Google API Configuration
GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_GENAI_USE_VERTEXAI=true  # Uncomment to use Vertex AI instead

# CodeInsight API Configuration
CODEINSIGHT_API_KEY=your_codeinsight_api_key_here

# MCP Server Configuration
MCP_SERVER_URL=http://localhost:8000

# QDE Configuration (optional)
QDE_CONFIG_PATH=/var/qde-ceph/liulizhu/projects/qde_feature_v2/examples/python/conf/qde.json

# A2A Configuration (for agent executor)
A2A_AGENT_NAME=security-analysis-agent
A2A_AGENT_DESCRIPTION=Comprehensive security analysis using CodeInsight and QDE

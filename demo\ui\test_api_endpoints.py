#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly.

This script tests the internal API endpoints that the UI uses to communicate
with the ADKHostManager.
"""

import asyncio
import httpx
import json

async def test_api_endpoints():
    """Test all API endpoints."""
    base_url = "http://localhost:12000"
    
    print("🧪 Testing Demo UI API Endpoints")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        
        # Test 1: List conversations
        print("1. Testing /conversation/list...")
        try:
            response = await client.post(
                f"{base_url}/conversation/list",
                json={"method": "conversation/list"},
                timeout=5.0
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data}")
                print("   ✅ List conversations endpoint working")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Test 2: List tasks
        print("2. Testing /task/list...")
        try:
            response = await client.post(
                f"{base_url}/task/list",
                json={"method": "task/list"},
                timeout=5.0
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data}")
                print("   ✅ List tasks endpoint working")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Test 3: List agents
        print("3. Testing /agent/list...")
        try:
            response = await client.post(
                f"{base_url}/agent/list",
                json={"method": "agent/list"},
                timeout=5.0
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data}")
                print("   ✅ List agents endpoint working")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Test 4: Pending messages
        print("4. Testing /message/pending...")
        try:
            response = await client.post(
                f"{base_url}/message/pending",
                json={"method": "message/pending"},
                timeout=5.0
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data}")
                print("   ✅ Pending messages endpoint working")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Test 5: Create conversation
        print("5. Testing /conversation/create...")
        try:
            response = await client.post(
                f"{base_url}/conversation/create",
                json={"method": "conversation/create"},
                timeout=5.0
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data}")
                print("   ✅ Create conversation endpoint working")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def test_health_check():
    """Test basic health check."""
    print("\n🏥 Testing Health Check")
    print("=" * 30)
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:12000/", timeout=5.0)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✅ Demo UI is responding")
                return True
            else:
                print(f"❌ Unexpected status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Cannot connect to Demo UI: {e}")
        print("Make sure Demo UI is running:")
        print("  cd demo/ui")
        print("  python main.py")
        return False

async def main():
    """Run all tests."""
    print("🔍 Testing Demo UI Internal API")
    print("Make sure Demo UI is running on http://localhost:12000")
    print()
    
    # First check if UI is running
    if not await test_health_check():
        return 1
    
    # Test API endpoints
    await test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 Test completed!")
    print("If endpoints are working but UI shows connection errors,")
    print("the issue might be in the client-side request handling.")
    
    return 0

if __name__ == "__main__":
    exit(asyncio.run(main()))

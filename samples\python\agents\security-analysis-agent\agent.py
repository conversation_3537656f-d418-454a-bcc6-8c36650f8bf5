"""Security Analysis Agent using CrewAI and MCP tools."""

import base64
import json
import logging
import os
from collections.abc import AsyncIterable
from typing import Any, Optional
from uuid import uuid4

from crewai import LLM, Agent, Crew, Task
from crewai.process import Process
from dotenv import load_dotenv
from pydantic import BaseModel

from crewai_tools import MCPServerAdapter



from mcp_tools import (
    code_insight_security_analysis,
    qde_malicious_code_detection,
    mcp_server_health_check
)

load_dotenv()
logger = logging.getLogger(__name__)


class SecurityAnalysisResult(BaseModel):
    """Result model for security analysis."""
    id: Optional[str] = None
    analysis_type: Optional[str] = None
    file_type: Optional[str] = None
    file_path: Optional[str] = None
    code_content: Optional[str] = None
    security_findings: Optional[str] = None
    malicious_patterns: Optional[str] = None
    risk_level: Optional[str] = None
    recommendations: Optional[str] = None
    error: Optional[str] = None


class SecurityAnalysisAgent:
    """CrewAI agent for comprehensive security analysis."""
    
    SUPPORTED_CONTENT_TYPES = ['text/plain', 'application/octet-stream', '*/*']
    
    def __init__(self):
        """Initialize the security analysis agent."""
        # LLM initialization
        if os.getenv('GOOGLE_GENAI_USE_VERTEXAI'):
            self.model = LLM(model='vertex_ai/gemini-2.0-flash')
        elif os.getenv('GOOGLE_API_KEY'):
            self.model = LLM(
                model='gemini/gemini-2.0-flash',
                api_key=os.getenv('GOOGLE_API_KEY'),
            )
        else:
            raise ValueError("No LLM configuration found. Set GOOGLE_API_KEY or GOOGLE_GENAI_USE_VERTEXAI")
        
        # Security Analysis Agent
        self.security_analyst_agent = Agent(
            role='Security Analysis Expert',
            goal=(
                "Perform comprehensive security analysis on code samples using "
                "CodeInsight API and QDE engine to identify vulnerabilities, "
                "malicious patterns, and security risks."
            ),
            backstory=(
                "You are a cybersecurity expert specializing in static code analysis. "
                "You use advanced tools like CodeInsight for API-based security analysis "
                "and QDE engine for malicious code detection. Your expertise helps "
                "identify security vulnerabilities, backdoors, and suspicious code patterns."
            ),
            verbose=False,
            allow_delegation=False,
            tools=[
                code_insight_security_analysis,
                qde_malicious_code_detection,
                mcp_server_health_check
            ],
            llm=self.model,
        )
        
        # Security Analysis Task
        self.security_analysis_task = Task(
            description=(
                "Analyze the provided code or file for security issues:\n"
                "1. First check if MCP server is accessible using MCPServerHealthCheck\n"
                "2. If analyzing code content, use CodeInsightSecurityAnalysis with:\n"
                "   - api_key: {api_key}\n"
                "   - file_type: {file_type}\n"
                "   - code_content: {code_content}\n"
                "3. If analyzing a file path, use QDEMaliciousCodeDetection with:\n"
                "   - file_path: {file_path}\n"
                "4. Provide comprehensive analysis including:\n"
                "   - Security vulnerabilities found\n"
                "   - Malicious patterns detected\n"
                "   - Risk assessment\n"
                "   - Remediation recommendations\n"
                "5. Return results as structured JSON"
            ),
            expected_output='Comprehensive security analysis result as JSON string',
            agent=self.security_analyst_agent,
        )
        
        # Analysis Crew
        self.analysis_crew = Crew(
            agents=[self.security_analyst_agent],
            tasks=[self.security_analysis_task],
            process=Process.sequential,
            verbose=False,
        )
    
    def analyze_code_content(
        self, 
        code_content: str, 
        file_type: str = "Python",
        api_key: Optional[str] = None
    ) -> SecurityAnalysisResult:
        """Analyze code content for security issues.
        
        Args:
            code_content: The code content to analyze
            file_type: Type of code (Python, JavaScript, etc.)
            api_key: CodeInsight API key (uses env var if not provided)
            
        Returns:
            Security analysis result
        """
        if not api_key:
            api_key = os.getenv('CODEINSIGHT_API_KEY')
            if not api_key:
                return SecurityAnalysisResult(
                    id=uuid4().hex,
                    error="CodeInsight API key not provided and CODEINSIGHT_API_KEY not set"
                )
        
        inputs = {
            'api_key': api_key,
            'file_type': file_type,
            'code_content': code_content,
            'file_path': None  # Not used for code content analysis
        }
        
        return self._execute_analysis(inputs, "code_content")
    
    def analyze_file_path(self, file_path: str) -> SecurityAnalysisResult:
        """Analyze a file path for malicious code.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Security analysis result
        """
        inputs = {
            'api_key': None,  # Not used for QDE analysis
            'file_type': None,  # Not used for QDE analysis
            'code_content': None,  # Not used for QDE analysis
            'file_path': file_path
        }
        
        return self._execute_analysis(inputs, "file_path")
    
    def analyze_base64_content(
        self, 
        base64_content: str, 
        filename: str,
        api_key: Optional[str] = None
    ) -> SecurityAnalysisResult:
        """Analyze base64 encoded file content.
        
        Args:
            base64_content: Base64 encoded file content
            filename: Original filename for type detection
            api_key: CodeInsight API key (uses env var if not provided)
            
        Returns:
            Security analysis result
        """
        try:
            # Decode base64 content
            decoded_content = base64.b64decode(base64_content).decode('utf-8')
            
            # Determine file type from filename extension
            file_type = self._get_file_type_from_filename(filename)
            
            return self.analyze_code_content(decoded_content, file_type, api_key)
            
        except Exception as e:
            logger.error(f"Failed to decode base64 content: {e}")
            return SecurityAnalysisResult(
                id=uuid4().hex,
                error=f"Failed to decode base64 content: {str(e)}"
            )
    
    def _execute_analysis(self, inputs: dict, analysis_type: str) -> SecurityAnalysisResult:
        """Execute the security analysis crew.
        
        Args:
            inputs: Input parameters for the analysis
            analysis_type: Type of analysis being performed
            
        Returns:
            Security analysis result
        """
        logger.info(f"Starting {analysis_type} security analysis")
        
        try:
            # Execute the crew
            result = self.analysis_crew.kickoff(inputs)
            
            # Parse the result
            if hasattr(result, 'raw'):
                result_str = result.raw
            else:
                result_str = str(result)
            
            # Try to parse as JSON
            try:
                result_data = json.loads(result_str)
                return SecurityAnalysisResult(**result_data)
            except json.JSONDecodeError:
                # If not JSON, create result with raw output
                return SecurityAnalysisResult(
                    id=uuid4().hex,
                    analysis_type=analysis_type,
                    security_findings=result_str
                )
                
        except Exception as e:
            logger.error(f"Security analysis failed: {e}")
            return SecurityAnalysisResult(
                id=uuid4().hex,
                analysis_type=analysis_type,
                error=str(e)
            )
    
    def _get_file_type_from_filename(self, filename: str) -> str:
        """Determine file type from filename extension.
        
        Args:
            filename: The filename to analyze
            
        Returns:
            File type string
        """
        extension = filename.lower().split('.')[-1] if '.' in filename else ''
        
        type_mapping = {
            'py': 'Python',
            'js': 'JavaScript',
            'ts': 'TypeScript',
            'java': 'Java',
            'cpp': 'C++',
            'c': 'C',
            'cs': 'C#',
            'php': 'PHP',
            'rb': 'Ruby',
            'go': 'Go',
            'rs': 'Rust',
            'sh': 'Shell',
            'ps1': 'PowerShell',
            'sql': 'SQL',
        }
        
        return type_mapping.get(extension, 'Unknown')
    
    def invoke(self, query: str, **kwargs) -> SecurityAnalysisResult:
        """Main invoke method for A2A integration.
        
        Args:
            query: Analysis request or code content
            **kwargs: Additional parameters
            
        Returns:
            Security analysis result
        """
        # Extract parameters from kwargs
        file_path = kwargs.get('file_path')
        file_type = kwargs.get('file_type', 'Python')
        api_key = kwargs.get('api_key')
        base64_content = kwargs.get('base64_content')
        filename = kwargs.get('filename')
        
        # Determine analysis type and execute
        if file_path:
            return self.analyze_file_path(file_path)
        elif base64_content and filename:
            return self.analyze_base64_content(base64_content, filename, api_key)
        else:
            # Treat query as code content
            return self.analyze_code_content(query, file_type, api_key)
    
    async def stream(self, query: str) -> AsyncIterable[dict[str, Any]]:
        """Streaming not supported."""
        raise NotImplementedError('Streaming is not supported by CrewAI.')

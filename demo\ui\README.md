# Demo UI with File Upload Support

A comprehensive UI solution and host service to interact with the A2A agent framework, featuring file upload capabilities for agent processing.

## Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
# Windows
start_with_agents.bat

# Linux/Mac
chmod +x start_with_agents.sh
./start_with_agents.sh
```

### Option 2: Manual Setup

1. **Install dependencies:**
```bash
# Activate virtual environment
source ../../.venv/Scripts/activate  # Linux/Mac
# or .venv\Scripts\activate.bat      # Windows

# Install project
pip install -e ../../
```

2. **Start File Type Detection Agent:**
```bash
cd ../../samples/python/agents/file-type-detector
python __main__.py --host localhost --port 8001
```

3. **Start Demo UI (in another terminal):**
```bash
cd demo/ui
source ../../.venv/Scripts/activate
export GOOGLE_API_KEY=your_google_api_key  # Optional
python main.py
```

4. **Access the UI:**
   - Open http://localhost:12000 in your browser
   - File upload functionality will be available

## File Upload Features

### Supported Operations
- **Upload Files**: Click the attachment icon to upload files
- **File Preview**: See uploaded files before sending
- **Mixed Messages**: Send text + files in the same message
- **File Analysis**: Files are automatically sent to appropriate agents

### File Type Detection
When you upload a file:
1. File is converted to base64 and wrapped in FilePart
2. Sent to the File Type Detection Agent via A2A protocol
3. Agent analyzes using python-magic
4. Returns detailed file type information

## Troubleshooting

See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for detailed troubleshooting guide.

For more information, see the [main README](../README.md).

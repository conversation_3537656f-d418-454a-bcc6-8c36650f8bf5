@echo off
REM Demo UI Startup Script for Windows
REM This script helps start the demo UI with proper environment setup

echo 🚀 Starting A2A Demo UI...
echo.

REM Check if virtual environment exists
if not exist "..\..\venv\Scripts\activate.bat" (
    echo ❌ Virtual environment not found at ..\..\venv
    echo Please create and activate the virtual environment first:
    echo   cd ..\..
    echo   python -m venv .venv
    echo   .venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

REM Activate virtual environment
echo 📦 Activating virtual environment...
call ..\..\venv\Scripts\activate.bat

REM Check if required packages are installed
echo 🔍 Checking dependencies...
python -c "import mesop, a2a" >nul 2>&1
if errorlevel 1 (
    echo ❌ Required packages not installed. Installing dependencies...
    pip install -e ..\..
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if backend service is running
echo 🔗 Checking backend service connection...
curl -s http://localhost:12000/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Backend service not detected at localhost:12000
    echo    The demo UI will run in standalone mode with limited functionality.
    echo    To start the full multiagent system:
    echo    1. Open another terminal
    echo    2. cd samples\python\hosts\multiagent
    echo    3. ..\..\..\.venv\Scripts\activate
    echo    4. python __main__.py
    echo.
)

REM Set environment variables
if "%GOOGLE_API_KEY%"=="" set GOOGLE_API_KEY=
if "%A2A_UI_HOST%"=="" set A2A_UI_HOST=0.0.0.0
if "%A2A_UI_PORT%"=="" set A2A_UI_PORT=12000

echo 🌐 Starting demo UI on http://%A2A_UI_HOST%:%A2A_UI_PORT%
echo 📁 Working directory: %CD%
echo.

REM Start the demo UI
python main.py

pause

@echo off
REM Startup script for Windows that launches file type detection agent and demo UI

echo 🎯 Starting A2A Demo with File Type Detection Agent
echo ================================================

REM Check virtual environment
if not exist "..\..\venv\Scripts\activate.bat" (
    echo ❌ Virtual environment not found
    echo Please create virtual environment first:
    echo   cd ..\..
    echo   python -m venv .venv
    echo   .venv\Scripts\activate
    echo   pip install -e .
    pause
    exit /b 1
)

REM Activate virtual environment
echo 📦 Activating virtual environment...
call ..\..\venv\Scripts\activate.bat

REM Set environment variables
set FILE_TYPE_AGENT_HOST=localhost
set FILE_TYPE_AGENT_PORT=8001
set PYTHONPATH=%CD%\..\..\samples\python

echo 🚀 Starting File Type Detection Agent...
REM Start agent in background
start "File Type Agent" /min cmd /c "cd ..\..\samples\python\agents\file-type-detector && python __main__.py --host localhost --port 8001"

REM Wait for agent to start
echo ⏳ Waiting for agent to initialize...
timeout /t 5 /nobreak >nul

REM Check if agent is running
curl -s http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Agent may not be ready yet, but continuing...
    echo    If you see connection errors, wait a moment and refresh the page
) else (
    echo ✅ File Type Detection Agent is ready
)

echo.
echo 🌐 Starting Demo UI...
echo    File upload functionality will be available
echo    Navigate to http://localhost:12000 in your browser
echo.

REM Start demo UI
python main.py

echo.
echo 🧹 Demo UI stopped. Agent may still be running in background.
echo    To stop the agent, close the "File Type Agent" window or run:
echo    taskkill /f /im python.exe
pause

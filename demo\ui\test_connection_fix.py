#!/usr/bin/env python3
"""
Test script to verify the connection error fixes in host_agent_service.py

This script simulates the scenario where the backend service is not available
and verifies that the functions return appropriate default values instead of None.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__))))

# Mock the service client to simulate connection failures
class MockConversationClient:
    def __init__(self, server_url):
        self.server_url = server_url
    
    async def list_conversation(self, request):
        raise Exception("Connection failed")
    
    async def list_tasks(self, request):
        raise Exception("Connection failed")
    
    async def get_pending_messages(self, request):
        raise Exception("Connection failed")
    
    async def list_agents(self, request):
        raise Exception("Connection failed")

# Patch the import
import sys
from unittest.mock import Mock
sys.modules['service.client.client'] = Mock()
sys.modules['service.client.client'].ConversationClient = MockConversationClient

# Now import the functions to test
from state.host_agent_service import (
    ListConversations,
    GetTasks, 
    GetProcessingMessages,
    ListAgents,
    GetMessageAliases
)


async def test_connection_failures():
    """Test that all functions handle connection failures gracefully."""
    print("Testing connection failure handling...\n")
    
    # Test ListConversations
    print("Testing ListConversations()...")
    try:
        conversations = await ListConversations()
        assert conversations == [], f"Expected empty list, got {conversations}"
        print("✓ ListConversations() returns empty list on failure")
    except Exception as e:
        print(f"❌ ListConversations() failed: {e}")
    
    # Test GetTasks
    print("Testing GetTasks()...")
    try:
        tasks = await GetTasks()
        assert tasks == [], f"Expected empty list, got {tasks}"
        print("✓ GetTasks() returns empty list on failure")
    except Exception as e:
        print(f"❌ GetTasks() failed: {e}")
    
    # Test GetProcessingMessages
    print("Testing GetProcessingMessages()...")
    try:
        messages = await GetProcessingMessages()
        assert messages == {}, f"Expected empty dict, got {messages}"
        print("✓ GetProcessingMessages() returns empty dict on failure")
    except Exception as e:
        print(f"❌ GetProcessingMessages() failed: {e}")
    
    # Test ListAgents
    print("Testing ListAgents()...")
    try:
        agents = await ListAgents()
        assert agents == [], f"Expected empty list, got {agents}"
        print("✓ ListAgents() returns empty list on failure")
    except Exception as e:
        print(f"❌ ListAgents() failed: {e}")
    
    # Test GetMessageAliases
    print("Testing GetMessageAliases()...")
    try:
        aliases = GetMessageAliases()
        assert aliases == {}, f"Expected empty dict, got {aliases}"
        print("✓ GetMessageAliases() returns empty dict")
    except Exception as e:
        print(f"❌ GetMessageAliases() failed: {e}")


async def test_update_app_state():
    """Test that UpdateAppState handles None returns gracefully."""
    print("\nTesting UpdateAppState()...")
    
    # Create a mock AppState
    class MockAppState:
        def __init__(self):
            self.conversations = None
            self.task_list = None
            self.background_tasks = None
            self.message_aliases = None
    
    try:
        from state.host_agent_service import UpdateAppState
        
        state = MockAppState()
        await UpdateAppState(state, "test_conversation_id")
        
        # Verify state was updated with safe defaults
        assert isinstance(state.task_list, list), "task_list should be a list"
        assert isinstance(state.background_tasks, dict), "background_tasks should be a dict"
        assert isinstance(state.message_aliases, dict), "message_aliases should be a dict"
        
        print("✓ UpdateAppState() handles connection failures gracefully")
        
    except Exception as e:
        print(f"❌ UpdateAppState() failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all tests."""
    print("Testing connection failure fixes for demo UI...\n")
    
    try:
        await test_connection_failures()
        await test_update_app_state()
        
        print("\n🎉 All tests passed! Connection error handling is working correctly.")
        print("\nThe demo UI should now handle backend service unavailability gracefully.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))

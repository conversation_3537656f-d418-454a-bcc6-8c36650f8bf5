# Demo UI 故障排除指南

## 常见错误及解决方案

### 1. 连接错误：`All connection attempts failed`

**错误信息：**
```
Failed to list conversations: All connection attempts failed
Failed to list tasks All connection attempts failed
Failed to update state: 'NoneType' object is not iterable
```

**原因：**
Demo UI 前端无法连接到后端服务器（默认 `http://localhost:12000`）

**解决方案：**

#### 方案 A：启动完整的多代理系统（推荐）

1. **启动 HostAgent 后端服务：**
```bash
# 激活环境
source .venv/Scripts/activate  # Linux/Mac
# 或
.venv\Scripts\activate.bat     # Windows

# 启动 multiagent host
cd samples/python/hosts/multiagent
python __main__.py
```

2. **在另一个终端启动 Demo UI：**
```bash
# 激活环境
source .venv/Scripts/activate  # Linux/Mac
# 或  
.venv\Scripts\activate.bat     # Windows

# 启动 demo UI
cd demo/ui
python main.py
```

#### 方案 B：使用启动脚本

**Windows：**
```cmd
cd demo\ui
start_demo.bat
```

**Linux/Mac：**
```bash
cd demo/ui
chmod +x start_demo.sh
./start_demo.sh
```

#### 方案 C：独立模式运行

如果不需要多代理功能，Demo UI 现在可以在独立模式下运行：

```bash
cd demo/ui
source ../../.venv/Scripts/activate
python main.py
```

修复后的代码会优雅地处理连接失败，UI 仍然可用但功能有限。

### 2. 模块导入错误：`ModuleNotFoundError`

**错误信息：**
```
ModuleNotFoundError: No module named 'mesop'
ModuleNotFoundError: No module named 'a2a'
```

**解决方案：**

1. **确保激活了正确的虚拟环境：**
```bash
source .venv/Scripts/activate  # Linux/Mac
# 或
.venv\Scripts\activate.bat     # Windows
```

2. **安装依赖：**
```bash
pip install -e .
```

3. **验证安装：**
```bash
python -c "import mesop, a2a; print('Dependencies OK')"
```

### 3. 文件上传功能问题

**问题：** 文件上传按钮不显示或不工作

**解决方案：**

1. **检查 Mesop 版本：**
```bash
pip show mesop
```

2. **确保浏览器支持：**
- 使用现代浏览器（Chrome, Firefox, Safari, Edge）
- 启用 JavaScript

3. **检查文件大小限制：**
- Mesop 默认限制文件大小
- 大文件可能需要调整配置

### 4. API Key 配置问题

**问题：** Google API Key 对话框一直显示

**解决方案：**

1. **设置环境变量：**
```bash
export GOOGLE_API_KEY="your_api_key_here"
```

2. **或使用 Vertex AI：**
```bash
export GOOGLE_GENAI_USE_VERTEXAI=true
```

3. **通过 UI 设置：**
- 在弹出的对话框中输入 API Key
- 点击保存

### 5. 端口冲突

**错误信息：**
```
OSError: [Errno 48] Address already in use
```

**解决方案：**

1. **更改端口：**
```bash
export A2A_UI_PORT=12001
python main.py
```

2. **或终止占用端口的进程：**
```bash
# 查找占用端口的进程
lsof -i :12000  # Linux/Mac
netstat -ano | findstr :12000  # Windows

# 终止进程
kill -9 <PID>  # Linux/Mac
taskkill /PID <PID> /F  # Windows
```

### 6. 文件权限问题

**问题：** 无法读取或写入文件

**解决方案：**

1. **检查文件权限：**
```bash
ls -la demo/ui/  # Linux/Mac
```

2. **修复权限：**
```bash
chmod +x start_demo.sh  # Linux/Mac
```

## 调试技巧

### 1. 启用详细日志

在 `main.py` 中添加：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 检查网络连接

```bash
curl -v http://localhost:12000/health
```

### 3. 验证环境配置

```bash
python -c "
import os
print('GOOGLE_API_KEY:', bool(os.getenv('GOOGLE_API_KEY')))
print('A2A_UI_HOST:', os.getenv('A2A_UI_HOST', 'default'))
print('A2A_UI_PORT:', os.getenv('A2A_UI_PORT', 'default'))
"
```

## 获取帮助

如果问题仍然存在：

1. **检查日志输出** - 查看终端中的详细错误信息
2. **验证环境** - 确保所有依赖都正确安装
3. **重启服务** - 尝试重启所有服务
4. **清理缓存** - 删除 `__pycache__` 目录并重试

## 成功启动的标志

当一切正常工作时，您应该看到：

```
🚀 Starting A2A Demo UI...
📦 Activating virtual environment...
🔍 Checking dependencies...
🔗 Backend service connected ✓
🌐 Starting demo UI on http://0.0.0.0:12000
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:12000
```

然后可以在浏览器中访问 `http://localhost:12000` 使用文件上传功能。

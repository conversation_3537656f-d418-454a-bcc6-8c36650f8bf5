"""Security Analysis MCP Server implementation using FastMCP."""

import logging
import os
from typing import Optional

from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.utilities.logging import get_logger

from .tools import code_insight_analysis, qde_malicious_code_analysis

# Load environment variables
load_dotenv()

logger = get_logger(__name__)


def create_server(host: str = "localhost", port: int = 8000) -> FastMCP:
    """Create and configure the FastMCP server with security analysis tools.
    
    Args:
        host: Server host address
        port: Server port number
        
    Returns:
        Configured FastMCP server instance
    """
    logger.info("Initializing Security Analysis MCP Server")
    
    # Create FastMCP server instance
    mcp = FastMCP("security-analysis", host=host, port=port)
    
    @mcp.tool(
        name="code_insight_analysis",
        description="Perform security analysis on code samples through CodeInsight API"
    )
    async def code_insight_tool(api_key: str, file_type: str, prompt: str) -> str:
        """MCP tool wrapper for CodeInsight security analysis.
        
        Args:
            api_key: Authentication key for API access
            file_type: Type of the analyzed file (e.g., 'Python', 'JavaScript')
            prompt: Code content to analyze
            
        Returns:
            Security analysis result as string
        """
        logger.info(f"CodeInsight analysis requested for file type: {file_type}")
        try:
            result = await code_insight_analysis(api_key, file_type, prompt)
            logger.info("CodeInsight analysis completed successfully")
            return result
        except Exception as e:
            logger.error(f"CodeInsight analysis failed: {str(e)}")
            raise
    
    @mcp.tool(
        name="qde_malicious_code_analysis",
        description="Detect malicious code using QDE engine through static analysis"
    )
    async def qde_analysis_tool(input_path: str) -> str:
        """MCP tool wrapper for QDE malicious code analysis.

        Args:
            input_path: Path to the file or directory to analyze for malicious code

        Returns:
            QDE analysis result as string
        """
        logger.info(f"QDE analysis requested for path: {input_path}")
        try:
            result = await qde_malicious_code_analysis(input_path)
            logger.info("QDE analysis completed successfully")
            return result
        except Exception as e:
            logger.error(f"QDE analysis failed: {str(e)}")
            raise

    # Add health check endpoint
    @mcp.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "message": "Security Analysis MCP Server is running"}

    return mcp


def serve(
    host: str = "localhost", 
    port: int = 8000, 
    transport: str = "sse"
) -> None:
    """Start the Security Analysis MCP server.
    
    Args:
        host: Server host address
        port: Server port number  
        transport: Transport mechanism ('sse', 'stdio', etc.)
    """
    logger.info(f"Starting Security Analysis MCP Server on {host}:{port} with {transport} transport")
    
    # Create server instance
    mcp = create_server(host, port)
    
    # Start the server
    logger.info("Security Analysis MCP Server is ready to accept connections")
    mcp.run(transport=transport)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Security Analysis MCP Server")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--transport", default="sse", help="Transport mechanism")
    
    args = parser.parse_args()
    
    serve(host=args.host, port=args.port, transport=args.transport)

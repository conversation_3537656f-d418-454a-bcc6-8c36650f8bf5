"""Integration test for Security Analysis Agent and MCP Server."""

import asyncio
import logging
import os
import subprocess
import time
from typing import Optional

from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class IntegrationTester:
    """Test the complete integration between CrewAI agent and MCP server."""
    
    def __init__(self):
        self.mcp_server_process: Optional[subprocess.Popen] = None
        self.mcp_server_url = "http://localhost:8000"
    
    def start_mcp_server(self) -> bool:
        """Start the MCP server for testing.
        
        Returns:
            True if server started successfully, False otherwise
        """
        try:
            print("Starting MCP server...")
            
            # Start the MCP server in background
            self.mcp_server_process = subprocess.Popen(
                ["python", "-m", "security_analysis_mcp.server", 
                 "--host", "localhost", "--port", "8000"],
                cwd="../security-analysis-mcp",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            time.sleep(3)
            
            # Check if server is running
            if self.mcp_server_process.poll() is None:
                print("✅ MCP server started successfully")
                return True
            else:
                print("❌ MCP server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start MCP server: {e}")
            return False
    
    def stop_mcp_server(self):
        """Stop the MCP server."""
        if self.mcp_server_process:
            print("Stopping MCP server...")
            self.mcp_server_process.terminate()
            self.mcp_server_process.wait()
            print("✅ MCP server stopped")
    
    def test_mcp_server_health(self) -> bool:
        """Test MCP server health check.
        
        Returns:
            True if server is healthy, False otherwise
        """
        try:
            from mcp_tools import mcp_server_health_check
            
            print("Testing MCP server health...")
            result = mcp_server_health_check()
            
            if "running and accessible" in result:
                print("✅ MCP server health check passed")
                return True
            else:
                print(f"❌ MCP server health check failed: {result}")
                return False
                
        except Exception as e:
            print(f"❌ MCP server health check error: {e}")
            return False
    
    def test_code_insight_tool(self) -> bool:
        """Test CodeInsight analysis tool.
        
        Returns:
            True if test passed, False otherwise
        """
        try:
            from mcp_tools import code_insight_security_analysis
            
            print("Testing CodeInsight analysis tool...")
            
            # Test with sample vulnerable code
            test_code = """
import os
import subprocess

def execute_command(user_input):
    # Vulnerable: direct shell execution
    return subprocess.run(user_input, shell=True, capture_output=True)

user_cmd = input("Enter command: ")
result = execute_command(user_cmd)
print(result.stdout)
"""
            
            api_key = os.getenv('CODEINSIGHT_API_KEY')
            if not api_key:
                print("⚠️  Skipping CodeInsight test - no API key provided")
                return True
            
            result = code_insight_security_analysis(
                api_key=api_key,
                file_type="Python",
                code_content=test_code
            )
            
            if "Error:" not in result:
                print("✅ CodeInsight analysis tool test passed")
                return True
            else:
                print(f"❌ CodeInsight analysis tool test failed: {result}")
                return False
                
        except Exception as e:
            print(f"❌ CodeInsight analysis tool test error: {e}")
            return False
    
    def test_qde_tool(self) -> bool:
        """Test QDE analysis tool.
        
        Returns:
            True if test passed, False otherwise
        """
        try:
            from mcp_tools import qde_malicious_code_detection
            
            print("Testing QDE analysis tool...")
            
            # Create a temporary test file
            test_file_path = "/tmp/test_security_analysis.py"
            test_content = """
# Test file for QDE analysis
import os
print("Hello, World!")
"""
            
            with open(test_file_path, 'w') as f:
                f.write(test_content)
            
            result = qde_malicious_code_detection(test_file_path)
            
            # Clean up test file
            os.remove(test_file_path)
            
            if "Error:" not in result:
                print("✅ QDE analysis tool test passed")
                return True
            else:
                print(f"⚠️  QDE analysis tool test result: {result}")
                # QDE might not be available in all environments
                return True
                
        except Exception as e:
            print(f"⚠️  QDE analysis tool test error (may be expected): {e}")
            # QDE might not be available in all environments
            return True
    
    def test_agent_integration(self) -> bool:
        """Test the complete agent integration.
        
        Returns:
            True if test passed, False otherwise
        """
        try:
            from agent import SecurityAnalysisAgent
            
            print("Testing Security Analysis Agent integration...")
            
            # Create agent instance
            agent = SecurityAnalysisAgent()
            
            # Test code content analysis
            test_code = """
import pickle
import base64

def load_data(data):
    # Vulnerable: pickle deserialization
    return pickle.loads(base64.b64decode(data))

user_data = input("Enter data: ")
result = load_data(user_data)
print(result)
"""
            
            print("  Testing code content analysis...")
            result = agent.analyze_code_content(
                code_content=test_code,
                file_type="Python"
            )
            
            if not result.error:
                print("  ✅ Code content analysis successful")
            else:
                print(f"  ❌ Code content analysis failed: {result.error}")
                return False
            
            # Test file path analysis (if QDE is available)
            print("  Testing file path analysis...")
            test_file_path = "/tmp/test_agent_integration.py"
            
            with open(test_file_path, 'w') as f:
                f.write(test_code)
            
            result = agent.analyze_file_path(test_file_path)
            
            # Clean up
            os.remove(test_file_path)
            
            if not result.error:
                print("  ✅ File path analysis successful")
            else:
                print(f"  ⚠️  File path analysis result: {result.error}")
                # This might fail if QDE is not available
            
            print("✅ Security Analysis Agent integration test passed")
            return True
            
        except Exception as e:
            print(f"❌ Security Analysis Agent integration test error: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all integration tests.
        
        Returns:
            True if all tests passed, False otherwise
        """
        print("=" * 60)
        print("SECURITY ANALYSIS INTEGRATION TESTS")
        print("=" * 60)
        
        try:
            # Start MCP server
            if not self.start_mcp_server():
                return False
            
            # Wait a bit more for server to be ready
            time.sleep(2)
            
            # Run tests
            tests = [
                ("MCP Server Health", self.test_mcp_server_health),
                ("CodeInsight Tool", self.test_code_insight_tool),
                ("QDE Tool", self.test_qde_tool),
                ("Agent Integration", self.test_agent_integration),
            ]
            
            results = []
            for test_name, test_func in tests:
                print(f"\n--- {test_name} Test ---")
                result = test_func()
                results.append((test_name, result))
            
            # Print summary
            print("\n" + "=" * 60)
            print("TEST SUMMARY")
            print("=" * 60)
            
            all_passed = True
            for test_name, result in results:
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"{test_name}: {status}")
                if not result:
                    all_passed = False
            
            print("=" * 60)
            if all_passed:
                print("🎉 ALL TESTS PASSED!")
            else:
                print("⚠️  SOME TESTS FAILED - Check logs above")
            
            return all_passed
            
        finally:
            # Always stop the server
            self.stop_mcp_server()


def main():
    """Main function to run integration tests."""
    logging.basicConfig(level=logging.INFO)
    
    # Check environment
    required_vars = ['GOOGLE_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        print("Please set these variables before running tests.")
        return False
    
    # Run tests
    tester = IntegrationTester()
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

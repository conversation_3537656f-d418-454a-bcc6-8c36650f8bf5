"""A2A Agent Executor for Security Analysis Agent."""

import base64
import json
import logging
import os
from typing import Any, Dict, List, Optional

from a2a.agent import Agent as A2AAgent
from a2a.agent.models import (
    AgentExecutorRequest,
    AgentExecutorResponse,
    AgentExecutorResponseStatus,
    Artifact,
    ArtifactType,
)
from dotenv import load_dotenv

from agent import SecurityAnalysisAgent, SecurityAnalysisResult

load_dotenv()

logger = logging.getLogger(__name__)


class SecurityAnalysisAgentExecutor:
    """A2A Agent Executor for Security Analysis."""
    
    def __init__(self):
        """Initialize the agent executor."""
        self.agent = SecurityAnalysisAgent()
        
        # A2A Agent configuration
        self.a2a_agent = A2AAgent(
            name="Security Analysis Agent",
            description=(
                "Comprehensive security analysis agent that uses CodeInsight API "
                "and QDE engine to identify vulnerabilities, malicious patterns, "
                "and security risks in code samples and files."
            ),
            supported_content_types=SecurityAnalysisAgent.SUPPORTED_CONTENT_TYPES,
        )
    
    def execute(self, request: AgentExecutorRequest) -> AgentExecutorResponse:
        """Execute security analysis based on the request.
        
        Args:
            request: The agent execution request
            
        Returns:
            Agent execution response with analysis results
        """
        try:
            logger.info(f"Executing security analysis for session: {request.session_id}")
            
            # Extract parameters from request
            query = request.query or ""
            artifacts = request.artifacts or []
            
            # Prepare analysis parameters
            analysis_params = self._extract_analysis_params(query, artifacts)
            
            # Execute analysis
            result = self._perform_analysis(query, analysis_params)
            
            # Create response artifacts
            response_artifacts = self._create_response_artifacts(result)
            
            # Determine response status
            status = (
                AgentExecutorResponseStatus.ERROR 
                if result.error 
                else AgentExecutorResponseStatus.SUCCESS
            )
            
            # Create summary message
            summary = self._create_summary_message(result)
            
            return AgentExecutorResponse(
                session_id=request.session_id,
                status=status,
                message=summary,
                artifacts=response_artifacts,
            )
            
        except Exception as e:
            logger.error(f"Agent execution failed: {e}")
            return AgentExecutorResponse(
                session_id=request.session_id,
                status=AgentExecutorResponseStatus.ERROR,
                message=f"Security analysis failed: {str(e)}",
                artifacts=[],
            )
    
    def _extract_analysis_params(self, query: str, artifacts: List[Artifact]) -> Dict[str, Any]:
        """Extract analysis parameters from query and artifacts.
        
        Args:
            query: User query
            artifacts: Input artifacts
            
        Returns:
            Dictionary of analysis parameters
        """
        params = {}
        
        # Check for file artifacts
        for artifact in artifacts:
            if artifact.type == ArtifactType.FILE:
                if artifact.content_type in ['text/plain', 'application/octet-stream']:
                    # Handle file content
                    if artifact.data:
                        params['base64_content'] = artifact.data
                        params['filename'] = artifact.name or 'unknown_file'
                elif artifact.path:
                    # Handle file path
                    params['file_path'] = artifact.path
        
        # Extract API key from query or environment
        if 'api_key:' in query.lower():
            # Extract API key from query
            parts = query.split('api_key:')
            if len(parts) > 1:
                api_key = parts[1].split()[0].strip()
                params['api_key'] = api_key
        
        # Extract file type from query
        if 'file_type:' in query.lower():
            parts = query.split('file_type:')
            if len(parts) > 1:
                file_type = parts[1].split()[0].strip()
                params['file_type'] = file_type
        
        return params
    
    def _perform_analysis(self, query: str, params: Dict[str, Any]) -> SecurityAnalysisResult:
        """Perform the security analysis.
        
        Args:
            query: User query
            params: Analysis parameters
            
        Returns:
            Security analysis result
        """
        # Use the agent's invoke method
        return self.agent.invoke(query, **params)
    
    def _create_response_artifacts(self, result: SecurityAnalysisResult) -> List[Artifact]:
        """Create response artifacts from analysis result.
        
        Args:
            result: Security analysis result
            
        Returns:
            List of response artifacts
        """
        artifacts = []
        
        # Create JSON artifact with full results
        json_data = result.model_dump_json(indent=2)
        json_artifact = Artifact(
            id=result.id or "security_analysis_result",
            name="security_analysis_result.json",
            type=ArtifactType.FILE,
            content_type="application/json",
            data=base64.b64encode(json_data.encode()).decode(),
        )
        artifacts.append(json_artifact)
        
        # Create text summary artifact if findings exist
        if result.security_findings or result.malicious_patterns:
            summary_text = self._create_detailed_summary(result)
            summary_artifact = Artifact(
                id=f"{result.id}_summary" if result.id else "security_summary",
                name="security_analysis_summary.txt",
                type=ArtifactType.FILE,
                content_type="text/plain",
                data=base64.b64encode(summary_text.encode()).decode(),
            )
            artifacts.append(summary_artifact)
        
        return artifacts
    
    def _create_summary_message(self, result: SecurityAnalysisResult) -> str:
        """Create a summary message for the response.
        
        Args:
            result: Security analysis result
            
        Returns:
            Summary message string
        """
        if result.error:
            return f"Security analysis failed: {result.error}"
        
        summary_parts = ["Security analysis completed successfully."]
        
        if result.security_findings:
            summary_parts.append("Security vulnerabilities detected.")
        
        if result.malicious_patterns:
            summary_parts.append("Malicious patterns found.")
        
        if result.risk_level:
            summary_parts.append(f"Risk level: {result.risk_level}")
        
        if result.recommendations:
            summary_parts.append("Recommendations provided.")
        
        return " ".join(summary_parts)
    
    def _create_detailed_summary(self, result: SecurityAnalysisResult) -> str:
        """Create a detailed text summary of the analysis.
        
        Args:
            result: Security analysis result
            
        Returns:
            Detailed summary text
        """
        lines = ["=== SECURITY ANALYSIS SUMMARY ===\n"]
        
        if result.analysis_type:
            lines.append(f"Analysis Type: {result.analysis_type}")
        
        if result.file_type:
            lines.append(f"File Type: {result.file_type}")
        
        if result.file_path:
            lines.append(f"File Path: {result.file_path}")
        
        if result.risk_level:
            lines.append(f"Risk Level: {result.risk_level}")
        
        lines.append("")
        
        if result.security_findings:
            lines.append("=== SECURITY FINDINGS ===")
            lines.append(result.security_findings)
            lines.append("")
        
        if result.malicious_patterns:
            lines.append("=== MALICIOUS PATTERNS ===")
            lines.append(result.malicious_patterns)
            lines.append("")
        
        if result.recommendations:
            lines.append("=== RECOMMENDATIONS ===")
            lines.append(result.recommendations)
            lines.append("")
        
        return "\n".join(lines)


def main():
    """Main function to run the agent executor."""
    logging.basicConfig(level=logging.INFO)
    
    executor = SecurityAnalysisAgentExecutor()
    
    # Register with A2A
    executor.a2a_agent.register_executor(executor.execute)
    
    logger.info("Security Analysis Agent is ready")
    
    # Keep the agent running
    try:
        executor.a2a_agent.run()
    except KeyboardInterrupt:
        logger.info("Agent stopped by user")


if __name__ == "__main__":
    main()

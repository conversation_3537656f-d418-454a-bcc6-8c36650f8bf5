"""A2A Agent Executor for Security Analysis Agent."""

import logging
import os
from typing import Any, Dict, List, Optional

from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.types import Part, TextPart
from a2a.utils import completed_task, new_agent_text_message, new_artifact
from a2a.utils.errors import ServerError
from agent import SecurityAnalysisAgent, SecurityAnalysisResult
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class SecurityAnalysisAgentExecutor(AgentExecutor):
    """A2A Agent Executor for Security Analysis."""

    def __init__(self):
        """Initialize the agent executor."""
        self.agent = SecurityAnalysisAgent()
    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        """Execute security analysis based on the request.

        Args:
            context: The request context containing user input and task information
            event_queue: Event queue for sending responses
        """
        query = context.get_user_input()

        try:
            logger.info(f"Executing security analysis for task: {context.task_id}")

            # Extract analysis parameters from query
            analysis_params = self._extract_analysis_params_from_query(query)

            # Execute analysis
            result = self.agent.invoke(query, **analysis_params)

            logger.info(f"Security analysis completed: {result.id}")

            # Create response parts
            parts = self._create_response_parts(result)

            # Send completed task event
            await event_queue.enqueue_event(
                completed_task(
                    context.task_id,
                    context.context_id,
                    [new_artifact(parts, f'SecurityAnalysis_{context.task_id}')],
                    [context.message],
                )
            )

        except Exception as e:
            logger.error(f"Security analysis failed: {e}")
            raise ServerError(
                error=ValueError(f"Security analysis failed: {e}")
            ) from e

    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        """Cancel the security analysis task.

        Args:
            context: The request context
            event_queue: Event queue for sending responses
        """
        raise Exception('cancel not supported')

    def _extract_analysis_params_from_query(self, query: str) -> Dict[str, Any]:
        """Extract analysis parameters from query.

        Args:
            query: User query containing analysis parameters

        Returns:
            Dictionary of analysis parameters
        """
        params = {}

        # Extract API key from query or environment
        if 'api_key:' in query.lower():
            # Extract API key from query
            parts = query.split('api_key:')
            if len(parts) > 1:
                api_key = parts[1].split()[0].strip()
                params['api_key'] = api_key

        # Extract file type from query
        if 'file_type:' in query.lower():
            parts = query.split('file_type:')
            if len(parts) > 1:
                file_type = parts[1].split()[0].strip()
                params['file_type'] = file_type

        # Extract file path from query
        if 'file_path:' in query.lower():
            parts = query.split('file_path:')
            if len(parts) > 1:
                file_path = parts[1].split()[0].strip()
                params['file_path'] = file_path

        return params

    def _create_response_parts(self, result: SecurityAnalysisResult) -> List[Part]:
        """Create response parts from analysis result.

        Args:
            result: Security analysis result

        Returns:
            List of response parts
        """
        parts = []

        # Create main text response
        response_text = self._create_summary_message(result)

        # Add detailed findings if available
        if result.security_findings:
            response_text += f"\n\n🔍 Security Findings:\n{result.security_findings}"

        if result.malicious_patterns:
            response_text += f"\n\n⚠️ Malicious Patterns:\n{result.malicious_patterns}"

        if result.recommendations:
            response_text += f"\n\n💡 Recommendations:\n{result.recommendations}"

        # Create text part
        parts.append(
            Part(
                root=TextPart(
                    text=response_text
                )
            )
        )

        return parts
    
    def _create_summary_message(self, result: SecurityAnalysisResult) -> str:
        """Create a summary message for the response.

        Args:
            result: Security analysis result

        Returns:
            Summary message string
        """
        if result.error:
            return f"❌ Security analysis failed: {result.error}"

        summary_parts = ["✅ Security analysis completed successfully."]

        if result.analysis_type:
            summary_parts.append(f"Analysis type: {result.analysis_type}")

        if result.file_type:
            summary_parts.append(f"File type: {result.file_type}")

        if result.risk_level:
            summary_parts.append(f"Risk level: {result.risk_level}")

        if result.security_findings:
            summary_parts.append("Security vulnerabilities detected.")

        if result.malicious_patterns:
            summary_parts.append("Malicious patterns found.")

        if result.recommendations:
            summary_parts.append("Recommendations provided.")

        return " ".join(summary_parts)


def main():
    """Main function to run the agent executor."""
    logging.basicConfig(level=logging.INFO)

    executor = SecurityAnalysisAgentExecutor()

    logger.info("Security Analysis Agent Executor is ready")
    logger.info("Use this executor with the A2A server framework")

    return executor


if __name__ == "__main__":
    main()

# Security Analysis Agent Usage Guide

This guide explains how to use the Security Analysis Agent for comprehensive code security analysis.

## Prerequisites

1. **Start the MCP Server**:
   ```bash
   cd ../security-analysis-mcp
   python -m security_analysis_mcp.server --host localhost --port 8000
   ```

2. **Set Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

## Usage Methods

### 1. Interactive Mode

Run the agent in interactive mode for testing:

```bash
python -m security_analysis_agent
```

Commands available:
- `health` - Check MCP server connectivity
- `code <file_type> <code_content>` - Analyze code content
- `file <file_path>` - Analyze file for malicious patterns
- `quit` - Exit

Example session:
```
security-agent> health
Health Check: MCP server is running and accessible

security-agent> code Python "import os; os.system(input('cmd: '))"
Analyzing Python code...
[Analysis results displayed]

security-agent> file /tmp/suspicious_script.py
Analyzing file: /tmp/suspicious_script.py
[Analysis results displayed]
```

### 2. A2A Agent Mode

Run as an A2A agent for integration with other systems:

```bash
python agent_executor.py
```

### 3. Programmatic Usage

```python
from agent import SecurityAnalysisAgent

# Create agent
agent = SecurityAnalysisAgent()

# Analyze code content
result = agent.analyze_code_content(
    code_content="import subprocess; subprocess.call(user_input, shell=True)",
    file_type="Python"
)

# Analyze file path
result = agent.analyze_file_path("/path/to/suspicious/file.py")

# Analyze base64 content
result = agent.analyze_base64_content(
    base64_content="aW1wb3J0IG9z...",  # base64 encoded content
    filename="script.py"
)

print(result.security_findings)
print(result.recommendations)
```

## Analysis Types

### CodeInsight Analysis
- **Purpose**: API-based security vulnerability detection
- **Input**: Code content + file type
- **Requirements**: CodeInsight API key
- **Output**: Security vulnerabilities, risk assessment

### QDE Analysis  
- **Purpose**: Malicious code pattern detection
- **Input**: File path
- **Requirements**: QDE engine access
- **Output**: Malicious patterns, behavioral analysis

## Input Formats

### Code Content Analysis
```python
# Direct code string
agent.analyze_code_content(
    code_content="your_code_here",
    file_type="Python"  # Python, JavaScript, Java, etc.
)
```

### File Path Analysis
```python
# Local file path
agent.analyze_file_path("/path/to/file.py")

# Directory analysis
agent.analyze_file_path("/path/to/directory")
```

### Base64 Content Analysis
```python
# Base64 encoded file content
agent.analyze_base64_content(
    base64_content="encoded_content",
    filename="original_filename.py"
)
```

## Response Format

All analysis methods return a `SecurityAnalysisResult` object:

```python
class SecurityAnalysisResult:
    id: str                    # Unique analysis ID
    analysis_type: str         # Type of analysis performed
    file_type: str            # Detected/specified file type
    file_path: str            # File path (if applicable)
    code_content: str         # Code content (if applicable)
    security_findings: str    # Security vulnerabilities found
    malicious_patterns: str   # Malicious patterns detected
    risk_level: str          # Overall risk assessment
    recommendations: str      # Remediation recommendations
    error: str               # Error message (if failed)
```

## Error Handling

Common errors and solutions:

1. **MCP Server Not Accessible**:
   ```
   Error: MCP server is not accessible
   ```
   Solution: Ensure MCP server is running on correct host/port

2. **Missing API Key**:
   ```
   Error: CodeInsight API key not provided
   ```
   Solution: Set `CODEINSIGHT_API_KEY` environment variable

3. **Invalid File Path**:
   ```
   Error: Invalid path: /nonexistent/file
   ```
   Solution: Verify file path exists and is accessible

4. **QDE Engine Error**:
   ```
   Error: QDE analysis failed
   ```
   Solution: Check QDE engine installation and configuration

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_API_KEY` | Google Gemini API key | Yes |
| `CODEINSIGHT_API_KEY` | CodeInsight API key | For code analysis |
| `MCP_SERVER_URL` | MCP server URL | Yes |
| `QDE_CONFIG_PATH` | QDE configuration path | For QDE analysis |

### Supported File Types

- Python (.py)
- JavaScript (.js)
- TypeScript (.ts)
- Java (.java)
- C/C++ (.c, .cpp)
- C# (.cs)
- PHP (.php)
- Ruby (.rb)
- Go (.go)
- Rust (.rs)
- Shell (.sh)
- PowerShell (.ps1)
- SQL (.sql)

## Best Practices

1. **Always check MCP server health** before analysis
2. **Use appropriate analysis type** for your use case:
   - CodeInsight for vulnerability detection
   - QDE for malicious pattern detection
3. **Handle errors gracefully** in production code
4. **Secure API keys** - never hardcode in source
5. **Validate file paths** before QDE analysis
6. **Review recommendations** carefully before implementing

## Troubleshooting

### MCP Server Issues
```bash
# Check if server is running
curl http://localhost:8000/tools

# Restart server if needed
cd ../security-analysis-mcp
python -m security_analysis_mcp.server
```

### API Key Issues
```bash
# Verify environment variables
echo $CODEINSIGHT_API_KEY
echo $GOOGLE_API_KEY

# Test API connectivity
python -c "import os; print('Keys set:', bool(os.getenv('CODEINSIGHT_API_KEY')))"
```

### QDE Engine Issues
```bash
# Check QDE installation
ls -la /var/qde-ceph/liulizhu/projects/qde_feature_v2/

# Verify configuration
cat /var/qde-ceph/liulizhu/projects/qde_feature_v2/examples/python/conf/qde.json
```

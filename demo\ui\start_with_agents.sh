#!/bin/bash

# Startup script for Linux/Mac that launches file type detection agent and demo UI

echo "🎯 Starting A2A Demo with File Type Detection Agent"
echo "================================================"

# Check virtual environment
if [ ! -d "../../.venv" ]; then
    echo "❌ Virtual environment not found"
    echo "Please create virtual environment first:"
    echo "  cd ../.."
    echo "  python -m venv .venv"
    echo "  source .venv/bin/activate"
    echo "  pip install -e ."
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source ../../.venv/bin/activate

# Set environment variables
export FILE_TYPE_AGENT_HOST=localhost
export FILE_TYPE_AGENT_PORT=8001
export PYTHONPATH="$(pwd)/../../samples/python"

echo "🚀 Starting File Type Detection Agent..."

# Start agent in background
cd ../../samples/python/agents/file-type-detector
python __main__.py --host localhost --port 8001 &
AGENT_PID=$!

# Return to demo UI directory
cd - > /dev/null

# Wait for agent to start
echo "⏳ Waiting for agent to initialize..."
sleep 5

# Check if agent is running
curl -s http://localhost:8001/health > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  Agent may not be ready yet, but continuing..."
    echo "   If you see connection errors, wait a moment and refresh the page"
else
    echo "✅ File Type Detection Agent is ready"
fi

echo ""
echo "🌐 Starting Demo UI..."
echo "   File upload functionality will be available"
echo "   Navigate to http://localhost:12000 in your browser"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Stopping File Type Detection Agent..."
    kill $AGENT_PID 2>/dev/null
    wait $AGENT_PID 2>/dev/null
    echo "✅ Agent stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start demo UI
python main.py

# Cleanup will be called automatically due to trap

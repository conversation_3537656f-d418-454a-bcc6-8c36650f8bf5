#!/usr/bin/env python3
"""
Startup script that launches both the file type detection agent and the demo UI.

This script:
1. Starts the file type detection agent in the background
2. Waits for it to be ready
3. Starts the demo UI with proper configuration
"""

import asyncio
import os
import subprocess
import sys
import time
import httpx
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root / "samples" / "python"))

async def check_agent_health(url: str, max_retries: int = 30) -> bool:
    """Check if agent is healthy and responding."""
    async with httpx.AsyncClient() as client:
        for i in range(max_retries):
            try:
                response = await client.get(f"{url}health", timeout=2.0)
                if response.status_code == 200:
                    return True
            except Exception:
                pass
            
            print(f"Waiting for agent to start... ({i+1}/{max_retries})")
            await asyncio.sleep(1)
    
    return False

async def start_file_type_agent():
    """Start the file type detection agent."""
    agent_dir = project_root / "samples" / "python" / "agents" / "file-type-detector"
    
    print("🚀 Starting File Type Detection Agent...")
    
    # Set environment variables
    env = os.environ.copy()
    env['PYTHONPATH'] = str(project_root / "samples" / "python")
    
    # Start the agent process
    process = subprocess.Popen(
        [sys.executable, "__main__.py", "--host", "localhost", "--port", "8001"],
        cwd=agent_dir,
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Wait a moment for the process to start
    await asyncio.sleep(2)
    
    # Check if process is still running
    if process.poll() is not None:
        stdout, stderr = process.communicate()
        print(f"❌ Agent failed to start:")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return None
    
    # Check if agent is responding
    agent_url = "http://localhost:8001/"
    if await check_agent_health(agent_url):
        print(f"✅ File Type Detection Agent started successfully at {agent_url}")
        return process
    else:
        print("❌ Agent started but not responding to health checks")
        process.terminate()
        return None

def start_demo_ui():
    """Start the demo UI."""
    print("🌐 Starting Demo UI...")
    
    # Set environment variables for agent connection
    env = os.environ.copy()
    env['FILE_TYPE_AGENT_HOST'] = 'localhost'
    env['FILE_TYPE_AGENT_PORT'] = '8001'
    
    # Start demo UI
    demo_dir = Path(__file__).parent
    os.chdir(demo_dir)
    
    # Import and run the main function
    import main
    # The main.py will handle the rest

async def main():
    """Main startup function."""
    print("🎯 Starting A2A Demo with File Type Detection Agent")
    print("=" * 50)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment may not be activated")
        print("   Please run: source .venv/Scripts/activate")
    
    # Start file type detection agent
    agent_process = await start_file_type_agent()
    
    if agent_process is None:
        print("❌ Failed to start file type detection agent")
        return 1
    
    try:
        # Start demo UI
        print("\n" + "=" * 50)
        start_demo_ui()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Clean up agent process
        if agent_process:
            print("🧹 Stopping File Type Detection Agent...")
            agent_process.terminate()
            try:
                agent_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                agent_process.kill()
            print("✅ Agent stopped")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(1)

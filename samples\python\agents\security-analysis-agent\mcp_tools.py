"""MCP client tools for CrewAI integration."""

import asyncio
import json
import logging
import os
from typing import Any, Dict, Optional

import aiohttp
from crewai.tools import tool
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

# MCP Server configuration
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:8000")


class MCPClient:
    """Client for communicating with MCP server."""
    
    def __init__(self, base_url: str = MCP_SERVER_URL):
        self.base_url = base_url
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """Call a tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to call
            parameters: Parameters to pass to the tool
            
        Returns:
            Tool execution result as string
        """
        url = f"{self.base_url}/tools/{tool_name}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=parameters) as response:
                    response.raise_for_status()
                    result = await response.json()
                    
                    # Extract the actual result from MCP response
                    if isinstance(result, dict) and "result" in result:
                        return str(result["result"])
                    return str(result)
                    
        except aiohttp.ClientError as e:
            error_msg = f"MCP tool call failed for {tool_name}: {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"
        except Exception as e:
            error_msg = f"Unexpected error calling {tool_name}: {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"


# Global MCP client instance
mcp_client = MCPClient()


@tool('CodeInsightSecurityAnalysis')
def code_insight_security_analysis(api_key: str, file_type: str, code_content: str) -> str:
    """Perform security analysis on code samples using CodeInsight API via MCP server.
    
    Args:
        api_key: Authentication key for CodeInsight API access
        file_type: Type of the analyzed file (e.g., 'Python', 'JavaScript', 'Java')
        code_content: The actual code content to analyze for security issues
        
    Returns:
        Security analysis result detailing any vulnerabilities or issues found
    """
    if not api_key or not file_type or not code_content:
        return "Error: All parameters (api_key, file_type, code_content) are required"
    
    logger.info(f"Requesting CodeInsight analysis for {file_type} code")
    
    parameters = {
        "api_key": api_key,
        "file_type": file_type,
        "prompt": code_content
    }
    
    # Run async function in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(
            mcp_client.call_tool("code_insight_analysis", parameters)
        )
        return result
    finally:
        loop.close()


@tool('QDEMaliciousCodeDetection')
def qde_malicious_code_detection(file_path: str) -> str:
    """Detect malicious code patterns using QDE engine via MCP server.
    
    Args:
        file_path: Path to the file or directory to analyze for malicious code patterns
        
    Returns:
        QDE analysis result indicating any malicious patterns or suspicious behaviors detected
    """
    if not file_path:
        return "Error: file_path parameter is required"
    
    logger.info(f"Requesting QDE analysis for path: {file_path}")
    
    parameters = {
        "input_path": file_path
    }
    
    # Run async function in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(
            mcp_client.call_tool("qde_malicious_code_analysis", parameters)
        )
        return result
    finally:
        loop.close()


@tool('MCPServerHealthCheck')
def mcp_server_health_check() -> str:
    """Check if the MCP server is running and accessible.
    
    Returns:
        Status message indicating server availability
    """
    logger.info("Checking MCP server health")
    
    # Run async function in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        async def check_health():
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{MCP_SERVER_URL}/health") as response:
                        if response.status == 200:
                            return "MCP server is running and accessible"
                        else:
                            return f"MCP server responded with status {response.status}"
            except Exception as e:
                return f"MCP server is not accessible: {str(e)}"
        
        result = loop.run_until_complete(check_health())
        return result
    finally:
        loop.close()

# Security Analysis Agent

A CrewAI-based agent that performs comprehensive security analysis on code samples using MCP (Model Context Protocol) tools.

## Features

- **Code Security Analysis**: Uses CodeInsight API for comprehensive security analysis
- **Malicious Code Detection**: Leverages QDE engine for detecting malicious patterns
- **MCP Integration**: Connects to MCP server for tool execution
- **CrewAI Framework**: Built using CrewAI for intelligent agent orchestration

## Architecture

This agent connects to the Security Analysis MCP Server to access two main tools:
1. **CodeInsight Analysis**: API-based security analysis for code samples
2. **QDE Analysis**: Static analysis for malicious code detection

## Installation

```bash
cd samples/python/agents/security-analysis-agent
pip install -e .
```

## Usage

### Prerequisites

1. Start the Security Analysis MCP Server:
```bash
cd ../security-analysis-mcp
python -m security_analysis_mcp.server --host localhost --port 8000
```

2. Set environment variables:
```bash
export GOOGLE_API_KEY="your-google-api-key"
export CODEINSIGHT_API_KEY="your-codeinsight-api-key"
export MCP_SERVER_URL="http://localhost:8000"
```

### Running the Agent

```bash
python -m security_analysis_agent
```

### A2A Integration

The agent can be deployed as an A2A agent:

```bash
python agent_executor.py
```

## Configuration

Environment variables:
- `GOOGLE_API_KEY`: Google Gemini API key for LLM
- `CODEINSIGHT_API_KEY`: API key for CodeInsight service
- `MCP_SERVER_URL`: URL of the MCP server (default: http://localhost:8000)
- `QDE_CONFIG_PATH`: Path to QDE configuration (optional)

## Example Usage

The agent can analyze code for:
- Security vulnerabilities
- Malicious patterns
- Code quality issues
- Suspicious behaviors

Input formats supported:
- Raw code text
- File paths
- Base64 encoded files

## Development

Install development dependencies:
```bash
pip install -e ".[dev]"
```

Run tests:
```bash
pytest
```

Format code:
```bash
black .
ruff check --fix .
```

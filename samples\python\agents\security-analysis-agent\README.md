# Security Analysis Agent

A CrewAI-based agent that performs comprehensive security analysis on code samples using MCP (Model Context Protocol) tools via MCPServerAdapter.

## Features

- **Code Security Analysis**: Uses CodeInsight API for comprehensive security analysis
- **Malicious Code Detection**: Leverages QDE engine for detecting malicious patterns
- **MCP Integration**: Uses MCPServerAdapter to connect to MCP server and get tools dynamically
- **CrewAI Framework**: Built using CrewAI for intelligent agent orchestration
- **Automatic Cleanup**: Proper MCP server connection lifecycle management

## Architecture

This agent uses `MCPServerAdapter` from `crewai_tools` to connect to the Security Analysis MCP Server:
1. **Dynamic Tool Loading**: Tools are loaded from MCP server at runtime
2. **CodeInsight Analysis**: API-based security analysis for code samples
3. **QDE Analysis**: Static analysis for malicious code detection
4. **Connection Management**: Automatic connection setup and cleanup

## Installation

```bash
cd samples/python/agents/security-analysis-agent
pip install -e .
```

## Usage

### Prerequisites

1. Start the Security Analysis MCP Server:
```bash
cd ../security-analysis-mcp
python -m security_analysis_mcp.server --host localhost --port 8000
```

2. Set environment variables:
```bash
export GOOGLE_API_KEY="your-google-api-key"
export CODEINSIGHT_API_KEY="your-codeinsight-api-key"
export MCP_SERVER_URL="http://localhost:8000"
```

### Running the Agent

#### Quick Demo
```bash
python start_demo.py
```

#### Interactive Mode
```bash
python -m security_analysis_agent
```

#### A2A Integration
```bash
# Test the executor
python test_executor.py

# Use with A2A server framework
from agent_executor import SecurityAnalysisAgentExecutor
executor = SecurityAnalysisAgentExecutor()
```

## MCP Integration Details

The agent uses `MCPServerAdapter` from `crewai_tools` to dynamically connect to the MCP server:

```python
server_params = {
    "url": "http://localhost:8000/sse",  # SSE server URL
    "transport": "sse"
}

mcp_server = MCPServerAdapter(server_params)
tools = mcp_server.tools  # Get tools dynamically
# ... use tools in CrewAI agent
mcp_server.stop()  # Cleanup after use
```

Key benefits:
- **Dynamic Tool Loading**: Tools are loaded from MCP server at runtime
- **Automatic Cleanup**: Proper connection lifecycle management
- **Error Handling**: Robust error handling for connection issues

## A2A Executor Pattern

The agent executor follows the new A2A pattern with async execution:

```python
class SecurityAnalysisAgentExecutor(AgentExecutor):
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        query = context.get_user_input()
        result = self.agent.invoke(query)

        # Create response parts
        parts = self._create_response_parts(result)

        # Send completed task event
        await event_queue.enqueue_event(
            completed_task(context.task_id, context.context_id,
                          [new_artifact(parts, f'SecurityAnalysis_{context.task_id}')],
                          [context.message])
        )

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        raise Exception('cancel not supported')
```

## Configuration

Environment variables:
- `GOOGLE_API_KEY`: Google Gemini API key for LLM
- `CODEINSIGHT_API_KEY`: API key for CodeInsight service
- `MCP_SERVER_URL`: URL of the MCP server (default: http://localhost:8000/sse)
- `QDE_CONFIG_PATH`: Path to QDE configuration (optional)

## Example Usage

The agent can analyze code for:
- Security vulnerabilities
- Malicious patterns
- Code quality issues
- Suspicious behaviors

Input formats supported:
- Raw code text
- File paths
- Base64 encoded files

## Development

Install development dependencies:
```bash
pip install -e ".[dev]"
```

Run tests:
```bash
pytest
```

Format code:
```bash
black .
ruff check --fix .
```

[project]
name = "security-analysis-mcp"
version = "0.1.0"
description = "MCP server for security analysis tools (CodeInsight and QDE)"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastmcp>=0.2.0",
    "aiohttp>=3.8.0",
    "asyncio-subprocess>=0.1.0",
    "pathlib>=1.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.ruff]
line-length = 88
target-version = "py312"

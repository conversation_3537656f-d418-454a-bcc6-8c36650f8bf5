[project]
name = "a2a-samples-security-analysis-agent"
version = "0.1.0"
description = "CrewAI agent for security analysis using MCP tools"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crewai[tools]>=0.95.0",
    "google-genai>=1.9.0",
    "a2a-sdk>=0.3.0",
    "aiohttp>=3.8.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "click>=8.0.0",
    "fastmcp>=0.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.ruff]
line-length = 88
target-version = "py312"

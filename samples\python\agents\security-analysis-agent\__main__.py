"""Main entry point for Security Analysis Agent."""

import logging
import sys
from agent import SecurityAnalysisAgent


def main():
    """Main function for testing the agent."""
    logging.basicConfig(level=logging.INFO)
    
    # Create agent instance
    agent = SecurityAnalysisAgent()
    
    print("Security Analysis Agent - Interactive Mode")
    print("=" * 50)
    print("Commands:")
    print("  code <file_type> <code_content>  - Analyze code content")
    print("  file <file_path>                 - Analyze file path")
    print("  health                           - Check MCP server health")
    print("  quit                             - Exit")
    print()
    
    while True:
        try:
            user_input = input("security-agent> ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            parts = user_input.split(' ', 2)
            command = parts[0].lower()
            
            if command == 'health':
                # Check MCP server health
                from mcp_tools import mcp_server_health_check
                result = mcp_server_health_check()
                print(f"Health Check: {result}")
            
            elif command == 'code' and len(parts) >= 3:
                # Analyze code content
                file_type = parts[1]
                code_content = parts[2]
                
                print(f"Analyzing {file_type} code...")
                result = agent.analyze_code_content(code_content, file_type)
                print_analysis_result(result)
            
            elif command == 'file' and len(parts) >= 2:
                # Analyze file path
                file_path = parts[1]
                
                print(f"Analyzing file: {file_path}")
                result = agent.analyze_file_path(file_path)
                print_analysis_result(result)
            
            else:
                print("Invalid command. Use 'code <file_type> <code>', 'file <path>', 'health', or 'quit'")
        
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


def print_analysis_result(result):
    """Print analysis result in a formatted way."""
    print("\n" + "=" * 60)
    print("SECURITY ANALYSIS RESULT")
    print("=" * 60)
    
    if result.error:
        print(f"❌ Error: {result.error}")
        return
    
    if result.analysis_type:
        print(f"Analysis Type: {result.analysis_type}")
    
    if result.file_type:
        print(f"File Type: {result.file_type}")
    
    if result.risk_level:
        print(f"Risk Level: {result.risk_level}")
    
    if result.security_findings:
        print(f"\n🔍 Security Findings:")
        print(result.security_findings)
    
    if result.malicious_patterns:
        print(f"\n⚠️  Malicious Patterns:")
        print(result.malicious_patterns)
    
    if result.recommendations:
        print(f"\n💡 Recommendations:")
        print(result.recommendations)
    
    print("=" * 60)


if __name__ == "__main__":
    main()

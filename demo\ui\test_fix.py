#!/usr/bin/env python3
"""
Simple test to verify the API routing fix.
"""

import subprocess
import time
import sys
import os

def test_curl_commands():
    """Test API endpoints using curl commands."""
    print("🧪 Testing API endpoints with curl...")
    
    base_url = "http://127.0.0.1:12000"
    
    endpoints = [
        "/conversation/list",
        "/task/list", 
        "/agent/list",
        "/message/pending"
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 Testing {endpoint}...")
        
        # Construct curl command
        cmd = [
            'curl', 
            '-X', 'POST',
            '-H', 'Content-Type: application/json',
            '-d', f'{{"method": "{endpoint[1:]}"}}',
            f'{base_url}{endpoint}',
            '--connect-timeout', '5',
            '--max-time', '10'
        ]
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=15
            )
            
            print(f"   Status: {result.returncode}")
            if result.returncode == 0:
                print(f"   Response: {result.stdout[:200]}...")
                print("   ✅ Endpoint accessible")
            else:
                print(f"   Error: {result.stderr}")
                print("   ❌ Endpoint failed")
                
        except subprocess.TimeoutExpired:
            print("   ❌ Request timed out")
        except FileNotFoundError:
            print("   ❌ curl not found - please install curl or test manually")
            break
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_ui_redirect():
    """Test UI redirect."""
    print("\n🌐 Testing UI redirect...")
    
    cmd = [
        'curl',
        '-I',  # HEAD request
        'http://127.0.0.1:12000/',
        '--connect-timeout', '5'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            if '302' in result.stdout or 'Location:' in result.stdout:
                print("   ✅ Root redirect working")
            else:
                print("   ⚠️  Root accessible but no redirect detected")
                print(f"   Response: {result.stdout}")
        else:
            print(f"   ❌ Error: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    """Main test function."""
    print("🔧 Testing Demo UI API Fix")
    print("=" * 40)
    print("Make sure Demo UI is running:")
    print("  python main.py")
    print()
    
    # Wait a moment for user to start the server
    print("Press Enter when Demo UI is running...")
    input()
    
    test_curl_commands()
    test_ui_redirect()
    
    print("\n" + "=" * 40)
    print("📋 Summary:")
    print("- If API endpoints are working, the routing fix is successful")
    print("- You should now be able to access the UI without connection errors")
    print("- Access the UI at: http://127.0.0.1:12000/ui")
    print("- Root URL (http://127.0.0.1:12000/) should redirect to /ui")

if __name__ == "__main__":
    main()

"""Security analysis tools converted to MCP format."""

import asyncio
import aiohttp
from pathlib import Path
from typing import Optional
from mcp.server.fastmcp import FastMCP


async def code_insight_analysis(
    api_key: str, 
    file_type: str, 
    prompt: str
) -> str:
    """Perform security analysis on code samples through CodeInsight API.
    
    Args:
        api_key: Authentication key for API access
        file_type: Type of the analyzed file (e.g., 'Python', 'JavaScript')
        prompt: Code content to analyze
        
    Returns:
        Security analysis result as string
        
    Raises:
        ValueError: If required parameters are missing
        RuntimeError: If API request fails
    """
    if not api_key or not file_type or not prompt:
        raise ValueError("All parameters (api_key, file_type, prompt) are required")
    
    endpoint = "security-analysis"
    base_url = "http://gpu34.jcpt.zzzc.qianxin-inc.cn:9002/analyze/"
    
    headers = {
        "Content-Type": "application/json",
    }
    
    payload = {
        "api_key": api_key,
        "file_type": file_type,
        "prompt": prompt
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}{endpoint}",
                headers=headers,
                json=payload
            ) as response:
                response.raise_for_status()
                return await response.text()
    except aiohttp.ClientError as e:
        raise RuntimeError(f"CodeInsight API request failed: {str(e)}")
    except Exception as e:
        raise RuntimeError(f"Error during CodeInsight analysis: {str(e)}")


async def qde_malicious_code_analysis(input_path: str) -> str:
    """Perform static code analysis to detect malicious patterns using QDE engine.
    
    Args:
        input_path: Path to the file or directory to analyze for malicious code
        
    Returns:
        QDE analysis result as string
        
    Raises:
        ValueError: If input path is invalid
        RuntimeError: If QDE analysis fails
    """
    if not input_path:
        raise ValueError("input_path parameter is required")
    
    resolved_path = Path(input_path).resolve()
    config_path = "/var/qde-ceph/liulizhu/projects/qde_feature_v2/examples/python/conf/qde.json"
    
    if not resolved_path.exists():
        raise ValueError(f"Invalid path: {input_path}")
    
    try:
        proc = await asyncio.create_subprocess_exec(
            "/bin/bash", "-c",
            f"cd /var/qde-ceph/liulizhu/projects/qde_feature_v2/examples/python && "
            f"source /var/qde-ceph/liulizhu/projects/pe_elf/.venv/bin/activate && "
            f"python3 qde_capi.py -i {str(resolved_path)} --conf {str(Path(config_path).resolve())}",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await proc.communicate()
        
        if proc.returncode != 0:
            raise RuntimeError(f"QDE analysis failed: {stderr.decode().strip()}")
        
        return stdout.decode().strip()
    except Exception as e:
        raise RuntimeError(f"Error during QDE analysis: {str(e)}")

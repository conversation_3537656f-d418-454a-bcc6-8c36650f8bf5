import base64
import dataclasses
import uuid

import mesop as me
from a2a.types import FilePart, FileWithBytes, Message, Part, Role, TextPart
from state.host_agent_service import (ListConversations, SendMessage,
                                      convert_message_to_state)
from state.state import AppState, StateMessage

from .chat_bubble import chat_bubble
from .form_render import form_sent, is_form, render_form


@me.stateclass
class PageState:
    """Local Page State"""

    conversation_id: str = ''
    message_content: str = ''
    uploaded_files: list[me.UploadedFile] = dataclasses.field(default_factory=list)


def on_blur(e: me.InputBlurEvent):
    """Input handler"""
    state = me.state(PageState)
    state.message_content = e.value


def on_file_upload(e: me.UploadEvent):
    """Handle file uploads"""
    state = me.state(PageState)
    state.uploaded_files = e.files


def remove_file(e: me.ClickEvent):
    """Remove a specific uploaded file"""
    state = me.state(PageState)
    file_index = int(e.key)
    if 0 <= file_index < len(state.uploaded_files):
        state.uploaded_files.pop(file_index)


def clear_uploaded_files():
    """Clear all uploaded files"""
    state = me.state(PageState)
    state.uploaded_files = []


async def send_message(message: str, message_id: str = ''):
    state = me.state(PageState)
    app_state = me.state(AppState)
    c = next(
        (
            x
            for x in await ListConversations()
            if x.conversation_id == state.conversation_id
        ),
        None,
    )
    if not c:
        print('Conversation id ', state.conversation_id, ' not found')

    # Build message parts
    parts = []

    # Add text part if there's text content
    if message.strip():
        parts.append(Part(root=TextPart(text=message)))

    # Add file parts
    for uploaded_file in state.uploaded_files:
        file_bytes = uploaded_file.getvalue()
        file_data = base64.b64encode(file_bytes).decode('utf-8')

        file_part = Part(root=FilePart(
            file=FileWithBytes(
                name=uploaded_file.name,
                bytes=file_data,
                mime_type=uploaded_file.mime_type or 'application/octet-stream'
            )
        ))
        parts.append(file_part)

    # Don't send empty messages
    if not parts:
        return

    request = Message(
        message_id=message_id,
        context_id=state.conversation_id,
        role=Role.user,
        parts=parts,
    )
    # Add message to state until refresh replaces it.
    state_message = convert_message_to_state(request)
    if not app_state.messages:
        app_state.messages = []
    app_state.messages.append(state_message)
    conversation = next(
        filter(
            lambda x: c and x.conversation_id == c.conversation_id,
            app_state.conversations,
        ),
        None,
    )
    if conversation:
        conversation.message_ids.append(state_message.message_id)
    await SendMessage(request)

    # Clear uploaded files after sending
    clear_uploaded_files()


async def send_message_enter(e: me.InputEnterEvent):  # pylint: disable=unused-argument
    """Send message handler"""
    yield
    state = me.state(PageState)
    state.message_content = e.value
    app_state = me.state(AppState)
    message_id = str(uuid.uuid4())
    app_state.background_tasks[message_id] = ''
    yield
    await send_message(state.message_content, message_id)
    # Clear message content after sending
    state.message_content = ''
    yield


async def send_message_button(e: me.ClickEvent):  # pylint: disable=unused-argument
    """Send message button handler"""
    yield
    state = me.state(PageState)
    app_state = me.state(AppState)
    message_id = str(uuid.uuid4())
    app_state.background_tasks[message_id] = ''
    await send_message(state.message_content, message_id)
    # Clear message content after sending
    state.message_content = ''
    yield


@me.component
def conversation():
    """Conversation component"""
    page_state = me.state(PageState)
    app_state = me.state(AppState)
    if 'conversation_id' in me.query_params:
        page_state.conversation_id = me.query_params['conversation_id']
        app_state.current_conversation_id = page_state.conversation_id
    with me.box(
        style=me.Style(
            display='flex',
            justify_content='space-between',
            flex_direction='column',
        )
    ):
        for message in app_state.messages:
            if is_form(message):
                render_form(message, app_state)
            elif form_sent(message, app_state):
                chat_bubble(
                    StateMessage(
                        message_id=message.message_id,
                        role=message.role,
                        content=[('Form submitted', 'text/plain')],
                    ),
                    message.message_id,
                )
            else:
                chat_bubble(message, message.message_id)

        # Input area with file upload support
        with me.box(
            style=me.Style(
                display='flex',
                flex_direction='column',
                gap=10,
                padding=me.Padding.all(10),
                min_width=500,
                width='100%',
            )
        ):
            # File preview area
            if page_state.uploaded_files:
                with me.box(
                    style=me.Style(
                        background='#f5f5f5',
                        padding=me.Padding.all(10),
                        border_radius=8,
                        border='1px solid #ddd',
                    )
                ):
                    me.text(
                        f'Uploaded Files ({len(page_state.uploaded_files)}):',
                        style=me.Style(font_weight='bold', margin=me.Margin(bottom=8))
                    )
                    for i, file in enumerate(page_state.uploaded_files):
                        with me.box(
                            style=me.Style(
                                display='flex',
                                gap=10,
                                align_items='center',
                                padding=me.Padding(top=4, bottom=4),
                            )
                        ):
                            me.icon('attach_file', style=me.Style(color='#666'))
                            with me.box(style=me.Style(flex_grow=1)):
                                me.text(
                                    file.name,
                                    style=me.Style(font_weight='500')
                                )
                                me.text(
                                    f'{file.size} bytes • {file.mime_type or "unknown type"}',
                                    style=me.Style(font_size='0.85em', color='#666')
                                )
                            with me.content_button(
                                type='icon',
                                on_click=remove_file,
                                key=str(i),
                                style=me.Style(color='#999')
                            ):
                                me.icon('close')

            # Input row with text, file upload, and send button
            with me.box(
                style=me.Style(
                    display='flex',
                    gap=8,
                    align_items='end',
                )
            ):
                # Text input
                me.input(
                    label='How can I help you?',
                    value=page_state.message_content,
                    on_blur=on_blur,
                    on_enter=send_message_enter,
                    style=me.Style(flex_grow=1),
                )

                # File upload button
                with me.content_uploader(
                    accepted_file_types=['*/*'],  # Accept all file types
                    on_upload=on_file_upload,
                    multiple=True,
                    type='icon',
                    color='primary',
                    style=me.Style(margin=me.Margin(bottom=8))
                ):
                    me.icon('attach_file')

                # Send button
                with me.content_button(
                    type='flat',
                    on_click=send_message_button,
                    disabled=not (page_state.message_content.strip() or page_state.uploaded_files),
                    style=me.Style(margin=me.Margin(bottom=8))
                ):
                    me.icon(icon='send')

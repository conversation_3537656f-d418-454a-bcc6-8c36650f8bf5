"""Main entry point for Security Analysis MCP Server."""

import sys
from .server import serve


def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] == "client":
        # Run client for testing
        from .client import main as client_main
        import asyncio
        asyncio.run(client_main())
    else:
        # Run server by default
        serve()


if __name__ == "__main__":
    main()

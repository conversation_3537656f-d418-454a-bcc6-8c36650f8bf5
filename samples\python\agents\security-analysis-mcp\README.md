# Security Analysis MCP Server

This project provides an MCP (Model Context Protocol) server that exposes security analysis tools for code analysis.

## Features

- **CodeInsight Tool**: Performs security analysis on code samples through CodeInsight API
- **QDE Analysis Tool**: Detects malicious code using QDE engine

## Tools Available

### 1. code_insight_analysis
Performs security analysis on code samples through CodeInsight API.

**Parameters:**
- `api_key` (string): Authentication key for API access
- `file_type` (string): Type of the analyzed file (e.g., 'Python', 'JavaScript')
- `prompt` (string): Code content to analyze

### 2. qde_malicious_code_analysis
Performs static code analysis to detect malicious patterns and suspicious behaviors using advanced QDE engine.

**Parameters:**
- `input_path` (string): Path to the file or directory to analyze for malicious code

## Installation

```bash
cd samples/python/agents/security-analysis-mcp
pip install -e .
```

## Usage

### Starting the MCP Server

```bash
python -m security_analysis_mcp.server --host localhost --port 8000 --transport sse
```

### Testing with Client

```bash
python -m security_analysis_mcp.client
```

## Configuration

Set the following environment variables:
- `CODEINSIGHT_API_KEY`: API key for CodeInsight service
- `QDE_CONFIG_PATH`: Path to QDE configuration file (optional)

## Development

Install development dependencies:
```bash
pip install -e ".[dev]"
```

Run tests:
```bash
pytest
```

Format code:
```bash
black .
ruff check --fix .
```

"""Test script for the new SecurityAnalysisAgentExecutor."""

import asyncio
import logging
import os
from unittest.mock import Mock

from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class MockRequestContext:
    """Mock RequestContext for testing."""
    
    def __init__(self, user_input: str, task_id: str = "test_task", context_id: str = "test_context"):
        self.user_input = user_input
        self.task_id = task_id
        self.context_id = context_id
        self.message = Mock()
    
    def get_user_input(self) -> str:
        return self.user_input


class MockEventQueue:
    """Mock EventQueue for testing."""
    
    def __init__(self):
        self.events = []
    
    async def enqueue_event(self, event):
        """Mock enqueue event method."""
        self.events.append(event)
        print(f"Event enqueued: {type(event).__name__}")


async def test_security_analysis_executor():
    """Test the SecurityAnalysisAgentExecutor with mock context."""
    print("=" * 60)
    print("🧪 TESTING SECURITY ANALYSIS AGENT EXECUTOR")
    print("=" * 60)
    
    # Check environment
    if not os.getenv('GOOGLE_API_KEY'):
        print("❌ GOOGLE_API_KEY not set. Please configure your environment.")
        return
    
    try:
        from agent_executor import SecurityAnalysisAgentExecutor
        
        # Create executor
        print("\n🤖 Creating SecurityAnalysisAgentExecutor...")
        executor = SecurityAnalysisAgentExecutor()
        print("✅ Executor created successfully")
        
        # Test 1: Simple code analysis
        print("\n" + "="*50)
        print("📝 Test 1: Code Analysis")
        print("="*50)
        
        vulnerable_code = """
import os
import subprocess

def run_command(user_input):
    # Security vulnerability: command injection
    os.system(user_input)

# Dangerous usage
user_cmd = input("Enter command: ")
run_command(user_cmd)
"""
        
        context = MockRequestContext(
            user_input=f"Analyze this Python code for security issues: {vulnerable_code}",
            task_id="test_code_analysis",
            context_id="test_context_1"
        )
        
        event_queue = MockEventQueue()
        
        print("Executing security analysis...")
        await executor.execute(context, event_queue)
        
        print(f"✅ Analysis completed. Events generated: {len(event_queue.events)}")
        
        # Test 2: File path analysis
        print("\n" + "="*50)
        print("📁 Test 2: File Path Analysis")
        print("="*50)
        
        context2 = MockRequestContext(
            user_input="file_path:/tmp/test_file.py Analyze this file for malicious patterns",
            task_id="test_file_analysis",
            context_id="test_context_2"
        )
        
        event_queue2 = MockEventQueue()
        
        print("Executing file path analysis...")
        try:
            await executor.execute(context2, event_queue2)
            print(f"✅ File analysis completed. Events generated: {len(event_queue2.events)}")
        except Exception as e:
            print(f"⚠️  File analysis result: {e} (may be expected if QDE not available)")
        
        # Test 3: Cancel operation
        print("\n" + "="*50)
        print("🛑 Test 3: Cancel Operation")
        print("="*50)
        
        try:
            await executor.cancel(context, event_queue)
        except Exception as e:
            print(f"✅ Cancel operation correctly raised exception: {e}")
        
        print("\n" + "="*60)
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_with_mcp_server():
    """Test with actual MCP server running."""
    print("\n" + "="*60)
    print("🔗 TESTING WITH MCP SERVER")
    print("="*60)
    
    import subprocess
    import time
    
    # Start MCP server
    print("🚀 Starting MCP server...")
    server_process = None
    
    try:
        server_process = subprocess.Popen(
            ["python", "-m", "security_analysis_mcp.server", 
             "--host", "localhost", "--port", "8000"],
            cwd="../security-analysis-mcp"
        )
        
        # Wait for server to start
        time.sleep(3)
        
        if server_process.poll() is None:
            print("✅ MCP Server started successfully")
            
            # Run the executor test
            await test_security_analysis_executor()
        else:
            print("❌ Failed to start MCP Server")
            
    except Exception as e:
        print(f"❌ Error with MCP server test: {e}")
    finally:
        # Stop MCP server
        if server_process:
            print("\n🛑 Stopping MCP Server...")
            server_process.terminate()
            server_process.wait()
            print("✅ MCP Server stopped")


def main():
    """Main function to run tests."""
    logging.basicConfig(level=logging.INFO)
    
    print("Choose test mode:")
    print("1. Test executor only (no MCP server)")
    print("2. Test with MCP server")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_security_analysis_executor())
    elif choice == "2":
        asyncio.run(test_with_mcp_server())
    else:
        print("Invalid choice. Running executor-only test...")
        asyncio.run(test_security_analysis_executor())


if __name__ == "__main__":
    main()
